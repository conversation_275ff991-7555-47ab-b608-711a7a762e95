
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String             @id
  email            String             @unique
  name             String?
  password         String
  role             UserRole           @default(USER)
  is2FAEnabled     Boolean?           @default(false)
  status           UserStatus         @default(ACTIVE)
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  orders           Order[]
  payments         Payment[]
  supportTickets   SupportTicket[]
  sessions         Session[]
  activityLog      ActivityLogEntry[]

  @@map("users")
}

model VPSPlan {
  id                    String              @id
  name                  String              @unique
  description           String              @db.Text
  pricePerMonth         Decimal             @db.Decimal(10, 2)
  originalPricePerMonth Decimal?            @db.Decimal(10, 2)
  discountLabel         String?
  specialTag            VpsPlanSpecialTag?
  cpuCores              Int
  ramGB                 Float // Representing GB, can be 0.5
  storageGB             Int
  bandwidthTB           Float
  features              String[]
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt
  orders                Order[]

  @@map("vps_plans")
}

model AppTheme {
  id                          String        @id
  name                        String        @unique
  settingsBackground          String        @map("settings_background")
  settingsForeground          String        @map("settings_foreground")
  settingsPrimary             String        @map("settings_primary")
  settingsPrimaryForeground   String        @map("settings_primary_foreground")
  settingsSecondary           String        @map("settings_secondary")
  settingsSecondaryForeground String        @map("settings_secondary_foreground")
  settingsAccent              String        @map("settings_accent")
  settingsAccentForeground    String        @map("settings_accent_foreground")
  createdAt                   DateTime      @default(now())
  updatedAt                   DateTime      @updatedAt
  siteSettings                SiteSettings? // Relation to SiteSettings if this theme is active

  @@map("app_themes")
}

model SiteSettings {
  id            String   @id // e.g., "global_settings"
  activeThemeId String   @unique // Foreign key to AppTheme.id
  activeTheme   AppTheme @relation(fields: [activeThemeId], references: [id], onDelete: Restrict) // Restrict deletion of active theme
  updatedAt     DateTime @updatedAt

  @@map("site_settings")
}

model Order {
  id                String       @id @default(cuid())
  userId            String
  user              User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  userEmail         String?
  planId            String
  plan              VPSPlan      @relation(fields: [planId], references: [id], onDelete: Restrict)
  vpsPlanName       String? // Denormalized for convenience
  orderDate         DateTime
  status            OrderStatus
  totalAmount       Decimal      @db.Decimal(10, 2)
  paymentMethod     String?
  billingAddress    String?      @db.Text
  ipAddress         String?      @unique // Assuming one VPS per order for unique IP
  fraudAnalysis     Json?
  vpsStatus         VpsStatus?
  operatingSystem   String?
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt
  payment           Payment[]

  @@index([userId])
  @@index([planId])
  @@map("orders")
}

model Payment {
  id            String        @id @default(cuid())
  orderId       String
  order         Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)
  userId        String
  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  paymentDate   DateTime
  amount        Decimal       @db.Decimal(10, 2)
  method        String
  transactionId String?
  status        PaymentStatus
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  @@index([orderId])
  @@index([userId])
  @@map("payments")
}

model SupportTicket {
  id          String             @id @default(cuid())
  userId      String
  user        User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  subject     String
  description String             @db.Text
  category    String
  priority    TicketPriorityEnum
  status      TicketStatusEnum
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  resolvedAt  DateTime?

  @@index([userId])
  @@index([status])
  @@map("support_tickets")
}

model Session {
  id         String   @id @default(cuid())
  sessionId  String   @unique @map("session_id")
  userId     String
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  expiresAt  DateTime @map("expires_at")
  userAgent  String?  @map("user_agent")
  ipAddress  String?  @map("ip_address")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([userId])
  @@map("sessions")
}

model ActivityLogEntry {
  id          String       @id @default(cuid())
  userId      String
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  timestamp   DateTime
  type        ActivityType
  description String       @db.Text
  icon        String?
  createdAt   DateTime     @default(now()) // Added for consistency

  @@index([userId])
  @@map("activity_log_entries")
}

model HomepageSlide {
  id                String   @id
  titleConfig       Json // Store the whole titleConfig object as JSON
  description       String   @db.Text
  cta1Text          String?
  cta1Href          String?
  cta1Variant       String? // Could be an enum if variants are fixed
  cta2Text          String?
  cta2Href          String?
  cta2Variant       String?
  backgroundClasses String?
  contentAlignment  String? // Could be an enum
  order             Int      @default(0) // For ordering slides
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("homepage_slides")
}

model TicketCategoryOption {
  id    String @id @default(cuid())
  name  String @unique
  order Int    @default(0)

  @@map("ticket_category_options")
}

model TicketPriorityOption {
  id    String             @id @default(cuid())
  value TicketPriorityEnum @unique // Maps to the actual enum value
  label String
  order Int                @default(0)

  @@map("ticket_priority_options")
}

enum UserRole {
  USER
  ADMIN

  @@map("user_role")
}

enum UserStatus {
  ACTIVE
  SUSPENDED
  PENDING_VERIFICATION

  @@map("user_status")
}

enum OrderStatus {
  PENDING
  ACTIVE
  CANCELLED
  FRAUD_REVIEW
  PROCESSING

  @@map("order_status")
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED

  @@map("payment_status")
}

enum TicketStatusEnum {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED

  @@map("ticket_status")
}

enum TicketPriorityEnum {
  LOW
  MEDIUM
  HIGH
  URGENT

  @@map("ticket_priority")
}

enum ActivityType {
  ORDER_PLACED
  PAYMENT_COMPLETED
  TICKET_CREATED
  VPS_STARTED
  VPS_STOPPED
  PROFILE_UPDATED
  LOGIN_SUCCESS
  GENERIC_NOTIFICATION // For general site announcements
  SIGNUP_SUCCESS

  @@map("activity_type")
}

enum VpsStatus {
  RUNNING
  STOPPED
  REBOOTING
  SUSPENDED
  PROVISIONING
  ERROR

  @@map("vps_status")
}

enum VpsPlanSpecialTag {
  MOST_BOUGHT
  BEST_PRICE
  NEW
  RECOMMENDED
  SALE

  @@map("vps_plan_special_tag")
}
