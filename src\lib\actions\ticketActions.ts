
// @ts-nocheck
'use server';

import type { SupportTicket, TicketPriority, TicketStatus } from '@/types';
import { logActivity } from './activityLogActions';
import prisma from './prisma';
import * as z from "zod";
import { TicketStatusEnum, TicketPriorityEnum, ActivityType, Prisma } from '@prisma/client';

const createTicketSchema = z.object({
  userId: z.string().min(1, "User ID is required."),
  subject: z.string().min(5, "Subject must be at least 5 characters.").max(100, "Subject cannot exceed 100 characters."),
  category: z.string({ required_error: "Please select a category." }).min(1, "Please select a category."),
  description: z.string().min(20, "Description must be at least 20 characters.").max(2000, "Description cannot exceed 2000 characters."),
});

type CreateTicketData = z.infer<typeof createTicketSchema>;

export interface TicketActionResult {
  success: boolean;
  ticket?: SupportTicket;
  error?: string;
  fieldErrors?: Partial<Record<keyof CreateTicketData, string[]>>;
}

export async function getTicketCategories(): Promise<string[]> {
  try {
    const categoriesFromDb = await prisma.ticketCategoryOption.findMany({
      orderBy: { order: 'asc' },
      select: { name: true }
    });
    return categoriesFromDb.map(c => c.name);
  } catch (error) {
    console.error('Error fetching ticket categories:', error);
    return [];
  }
}

export async function getTicketPriorities(): Promise<{ value: TicketPriorityEnum; label: string }[]> {
   try {
    const prioritiesFromDb = await prisma.ticketPriorityOption.findMany({
      orderBy: { order: 'asc' },
      select: { value: true, label: true }
    });
    return prioritiesFromDb.map(p => ({
      value: p.value, // Already TicketPriorityEnum
      label: p.label
    }));
  } catch (error) {
    console.error('Error fetching ticket priorities:', error);
    return [];
  }
}

export async function getUserTickets(userId: string): Promise<SupportTicket[]> {
  if (!userId) {
    return [];
  }
  try {
    const ticketsFromDb = await prisma.supportTicket.findMany({
      where: { userId },
      orderBy: { updatedAt: 'desc' }
    });
    return ticketsFromDb.map(t => ({
      ...t,
      createdAt: t.createdAt.toISOString(),
      updatedAt: t.updatedAt.toISOString(),
      resolvedAt: t.resolvedAt?.toISOString(),
    }));
  } catch (error) {
    console.error("Error fetching user tickets:", error);
    return [];
  }
}


export async function createTicket(ticketData: CreateTicketData): Promise<TicketActionResult> {
  const validationResult = createTicketSchema.safeParse(ticketData);
  if (!validationResult.success) {
    return {
      success: false,
      error: "Validation failed. Please check the fields below.",
      fieldErrors: validationResult.error.flatten().fieldErrors
    };
  }

  const validatedData = validationResult.data;

  try {
    const now = new Date();
    // Ensure category exists or handle appropriately
    const categoryExists = await prisma.ticketCategoryOption.findUnique({ where: { name: validatedData.category } });
    if (!categoryExists) {
        return { success: false, error: `Invalid category: ${validatedData.category}. Please select a valid category.`, fieldErrors: { category: [`Invalid category: ${validatedData.category}`] } };
    }

    const newTicketFromDb = await prisma.supportTicket.create({
        data: {
            userId: validatedData.userId,
            subject: validatedData.subject,
            category: validatedData.category, // This should be a string that exists in TicketCategoryOption
            priority: TicketPriorityEnum.MEDIUM, // Default priority
            description: validatedData.description,
            status: TicketStatusEnum.OPEN,
            createdAt: now,
            updatedAt: now,
        }
    });

    await logActivity(
      validatedData.userId,
      ActivityType.TICKET_CREATED,
      `Support ticket '${newTicketFromDb.subject.substring(0, 30)}...' created (ID: ${newTicketFromDb.id}).`,
      'Ticket'
    );

    return {
        success: true,
        ticket: {
            ...newTicketFromDb,
            createdAt: newTicketFromDb.createdAt.toISOString(),
            updatedAt: newTicketFromDb.updatedAt.toISOString(),
            resolvedAt: newTicketFromDb.resolvedAt?.toISOString(),
        }
    };
  } catch (error) {
    console.error("Error creating ticket:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to create support ticket.";
    return { success: false, error: errorMessage };
  }
}

export async function countUserTicketsByStatus(userId: string, status: TicketStatusEnum | TicketStatusEnum[]): Promise<number> {
  if (!userId) {
    return 0;
  }
  if (!status || (Array.isArray(status) && status.length === 0)) {
     console.warn("countUserTicketsByStatus called with no status");
    return 0;
  }
  try {
    const statusesToCount = Array.isArray(status) ? status : [status];
    return await prisma.supportTicket.count({
      where: { userId, status: { in: statusesToCount } },
    });
  } catch (error) {
    console.error(`Error counting tickets for user ${userId} with status ${status}:`, error);
    return 0;
  }
}

export async function countAllTicketsByStatus(status: TicketStatusEnum | TicketStatusEnum[]): Promise<number> {
  if (!status || (Array.isArray(status) && status.length === 0)) {
     console.warn("countAllTicketsByStatus called with no status");
    return 0;
  }
  try {
    const statusesToCount = Array.isArray(status) ? status : [status];
    return await prisma.supportTicket.count({
      where: { status: { in: statusesToCount } },
    });
  } catch (error) {
    console.error(`Error counting all tickets with status ${status}:`, error);
    return 0;
  }
}


export async function getAllTickets(): Promise<SupportTicket[]> {
  try {
    const ticketsFromDb = await prisma.supportTicket.findMany({
      orderBy: { updatedAt: 'desc' }
    });
    return ticketsFromDb.map(t => ({
      ...t,
      createdAt: t.createdAt.toISOString(),
      updatedAt: t.updatedAt.toISOString(),
      resolvedAt: t.resolvedAt?.toISOString(),
    }));
  } catch (error) {
    console.error("Error fetching all tickets:", error);
    return [];
  }
}

interface UpdateTicketResult {
  success: boolean;
  ticket?: SupportTicket;
  error?: string;
}

export async function updateTicket(ticketId: string, updates: Partial<Pick<SupportTicket, 'status' | 'priority'>>): Promise<UpdateTicketResult> {
  if (!ticketId) return { success: false, error: "Ticket ID is required." };
  if (!updates || (updates.status === undefined && updates.priority === undefined)) {
    return { success: false, error: "No updates provided." };
  }

  try {
    const originalTicket = await prisma.supportTicket.findUnique({ where: { id: ticketId } });
    if (!originalTicket) {
        return { success: false, error: 'Ticket not found.' };
    }

    const now = new Date();
    const dataToUpdate: Prisma.SupportTicketUpdateInput = {
        updatedAt: now,
    };

    if (updates.status) {
        dataToUpdate.status = updates.status as TicketStatusEnum; // Ensure type casting
        if ((updates.status === TicketStatusEnum.RESOLVED || updates.status === TicketStatusEnum.CLOSED) && !originalTicket.resolvedAt) {
            dataToUpdate.resolvedAt = now;
        }
        if ((updates.status === TicketStatusEnum.OPEN || updates.status === TicketStatusEnum.IN_PROGRESS) && originalTicket.resolvedAt) {
            dataToUpdate.resolvedAt = null;
        }
    }
    if (updates.priority) {
        dataToUpdate.priority = updates.priority as TicketPriorityEnum; // Ensure type casting
    }

    const updatedTicketFromDb = await prisma.supportTicket.update({
        where: { id: ticketId },
        data: dataToUpdate,
    });

    return {
        success: true,
        ticket: {
            ...updatedTicketFromDb,
            createdAt: updatedTicketFromDb.createdAt.toISOString(),
            updatedAt: updatedTicketFromDb.updatedAt.toISOString(),
            resolvedAt: updatedTicketFromDb.resolvedAt?.toISOString(),
        }
    };
  } catch (error) {
     if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return { success: false, error: 'Ticket not found.' };
    }
    console.error(`Error updating ticket ${ticketId}:`, error);
    return { success: false, error: 'Failed to update ticket.' };
  }
}
