
import { PrismaClient, Prisma, TicketStatusEnum, TicketPriorityEnum, ActivityType, VpsPlanSpecialTag } from '@prisma/client';
import type { UserRole, UserStatus, OrderStatus, PaymentStatus, VpsStatus } from '@prisma/client'; // Import as types
import bcryptjs from 'bcryptjs';

const prisma = new PrismaClient();

const usersSeedData: Omit<Prisma.UserCreateInput, 'password'> & { passwordRaw: string }[] = [
  { id: "user001", name: "Demo User", email: "<EMAIL>", passwordRaw: "password", role: "USER", is2FAEnabled: false, status: "ACTIVE" },
  { id: "admin001", name: "Admin SkyHosting", email: "<EMAIL>", passwordRaw: "adminpass", role: "ADMIN", is2FAEnabled: false, status: "ACTIVE" },
  { id: "user002", name: "2FA User", email: "<EMAIL>", passwordRaw: "password", role: "USER", is2FAEnabled: true, status: "ACTIVE" },
  { id: "user003", name: "High Risk User", email: "<EMAIL>", passwordRaw: "password", role: "USER", is2FAEnabled: false, status: "ACTIVE" }
];

const vpsPlansData: Prisma.VPSPlanCreateInput[] = [
  {
    id: "starter",
    name: "Starter VPS",
    description: "Perfect for small personal projects, blogs, and development environments. Get started quickly with essential resources.",
    pricePerMonth: 400,
    cpuCores: 1,
    ramGB: 1,
    storageGB: 25,
    bandwidthTB: 1,
    features: ["1 Dedicated IP", "SSD Storage", "Root Access", "DDoS Protection"],
    specialTag: VpsPlanSpecialTag.BEST_PRICE
  },
  {
    id: "developer",
    name: "Developer VPS",
    description: "Ideal for developers and growing applications. More power and resources to handle increased traffic and complexity.",
    originalPricePerMonth: 1600,
    pricePerMonth: 1200,
    discountLabel: "Save 25%",
    specialTag: VpsPlanSpecialTag.MOST_BOUGHT,
    cpuCores: 2,
    ramGB: 4,
    storageGB: 80,
    bandwidthTB: 2,
    features: ["1 Dedicated IP", "NVMe SSD Storage", "Root Access", "Enhanced DDoS Protection", "Weekly Backups"]
  },
  {
    id: "business",
    name: "Business VPS",
    description: "Robust solution for established businesses and high-traffic websites. Ensures reliability and performance for critical applications.",
    pricePerMonth: 3200,
    cpuCores: 4,
    ramGB: 8,
    storageGB: 160,
    bandwidthTB: 5,
    features: ["2 Dedicated IPs", "NVMe SSD Storage", "Full Root Access", "Advanced DDoS Protection", "Daily Backups", "Priority Support"],
    specialTag: VpsPlanSpecialTag.RECOMMENDED
  },
  {
    id: "enterprise",
    name: "Enterprise VPS",
    description: "Top-tier performance for large-scale applications and enterprise workloads. Maximum resources and premium features.",
    originalPricePerMonth: 7200,
    pricePerMonth: 6400,
    discountLabel: "Save ₹800",
    specialTag: VpsPlanSpecialTag.SALE,
    cpuCores: 8,
    ramGB: 16,
    storageGB: 320,
    bandwidthTB: 10,
    features: ["Multiple Dedicated IPs", "High-Performance NVMe", "Full Root Access", "Comprehensive DDoS Mitigation", "Managed Backups", "24/7 Premium Support"]
  },
  {
    id: "basic-ssd",
    name: "Basic SSD",
    description: "An affordable entry-level VPS with fast SSD storage, perfect for lightweight websites or testing environments.",
    pricePerMonth: 250,
    cpuCores: 1,
    ramGB: 0.5, // Prisma Float type can handle this
    storageGB: 15,
    bandwidthTB: 0.5,
    features: ["1 Dedicated IP", "SSD Storage", "Root Access", "Basic Firewall"],
    specialTag: VpsPlanSpecialTag.NEW
  },
  {
    id: "powerhouse-nvme",
    name: "Powerhouse NVMe",
    description: "Extreme performance with cutting-edge NVMe storage and abundant resources for demanding applications and databases.",
    pricePerMonth: 9000,
    cpuCores: 12,
    ramGB: 32,
    storageGB: 500,
    bandwidthTB: 15,
    features: ["Multiple Dedicated IPs", "Blazing Fast NVMe", "Full Root Access", "Advanced Security Suite", "Automated Scaling Options", "Dedicated Support Channel"]
  }
];

const themesData: Prisma.AppThemeCreateInput[] = [
  {
    id: "default",
    name: "Default (Sky Blue)",
    settingsBackground: "0 0% 94%",
    settingsForeground: "210 29% 10%",
    settingsPrimary: "210 29% 24%",
    settingsPrimaryForeground: "0 0% 98%",
    settingsSecondary: "210 30% 88%",
    settingsSecondaryForeground: "210 29% 24%",
    settingsAccent: "180 100% 25%",
    settingsAccentForeground: "0 0% 98%"
  },
  {
    id: "forest",
    name: "Forest Green",
    settingsBackground: "30 20% 95%",
    settingsForeground: "120 25% 15%",
    settingsPrimary: "120 60% 30%",
    settingsPrimaryForeground: "60 100% 95%",
    settingsSecondary: "120 20% 85%",
    settingsSecondaryForeground: "120 40% 25%",
    settingsAccent: "40 100% 50%",
    settingsAccentForeground: "30 50% 10%"
  },
  {
    id: "sunset",
    name: "Sunset Orange",
    settingsBackground: "20 50% 96%",
    settingsForeground: "25 50% 15%",
    settingsPrimary: "25 80% 50%",
    settingsPrimaryForeground: "0 0% 100%",
    settingsSecondary: "30 40% 90%",
    settingsSecondaryForeground: "25 60% 30%",
    settingsAccent: "0 100% 60%",
    settingsAccentForeground: "0 0% 100%"
  },
  {
    id: "lavender",
    name: "Lavender Bliss",
    settingsBackground: "270 30% 97%",
    settingsForeground: "270 25% 20%",
    settingsPrimary: "270 50% 45%",
    settingsPrimaryForeground: "0 0% 100%",
    settingsSecondary: "270 20% 90%",
    settingsSecondaryForeground: "270 40% 30%",
    settingsAccent: "300 100% 65%",
    settingsAccentForeground: "0 0% 100%"
  },
  {
    id: "ocean",
    name: "Ocean Blue",
    settingsBackground: "200 40% 96%",
    settingsForeground: "220 30% 18%",
    settingsPrimary: "210 70% 45%",
    settingsPrimaryForeground: "0 0% 100%",
    settingsSecondary: "200 30% 88%",
    settingsSecondaryForeground: "210 50% 30%",
    settingsAccent: "170 70% 40%",
    settingsAccentForeground: "0 0% 100%"
  }
];

const siteSettingsData: Prisma.SiteSettingsCreateInput = {
  id: "global_settings", 
  activeThemeId: "default",
};

const ordersData: Prisma.OrderCreateInput[] = [
  {
    id: "ORD101",
    userId: "user002",
    userEmail: "<EMAIL>",
    planId: "developer",
    vpsPlanName: "Developer VPS", 
    orderDate: new Date("2024-05-28T10:00:00.000Z"),
    status: "ACTIVE",
    totalAmount: 1200,
    paymentMethod: "UPI",
    ipAddress: "*************",
    billingAddress: "1 Infinite Loop, Cupertino, CA",
    vpsStatus: "RUNNING",
    operatingSystem: "Ubuntu 22.04 LTS",
    fraudAnalysis: Prisma.JsonNull, 
  },
  {
    id: "ORD102",
    userId: "user001",
    userEmail: "<EMAIL>",
    planId: "starter",
    vpsPlanName: "Starter VPS", 
    orderDate: new Date("2024-05-27T11:30:00.000Z"),
    status: "PENDING",
    totalAmount: 400,
    paymentMethod: "Credit Card", 
    ipAddress: "*************",
    billingAddress: "PO Box 123, Somewhere, XY",
    vpsStatus: "PROVISIONING",
    operatingSystem: "Debian 12",
    fraudAnalysis: Prisma.JsonNull,
  },
  {
    id: "ORD103",
    userId: "user001",
    userEmail: "<EMAIL>",
    planId: "business",
    vpsPlanName: "Business VPS", 
    orderDate: new Date("2024-05-26T09:15:00.000Z"),
    status: "ACTIVE",
    totalAmount: 3200,
    paymentMethod: "UPI",
    ipAddress: "*************",
    billingAddress: "456 Business Rd, Big City, ST",
    vpsStatus: "STOPPED",
    operatingSystem: "CentOS Stream 9",
    fraudAnalysis: Prisma.JsonNull,
  },
  {
    id: "ORD104",
    userId: "admin001",
    userEmail: "<EMAIL>",
    planId: "enterprise",
    vpsPlanName: "Enterprise VPS", 
    orderDate: new Date("2024-05-25T16:45:00.000Z"),
    status: "ACTIVE",
    totalAmount: 6400,
    paymentMethod: "UPI",
    ipAddress: "*************",
    billingAddress: "789 Corp Plaza, Metroville, ZP",
    vpsStatus: "SUSPENDED",
    operatingSystem: "AlmaLinux 9",
    fraudAnalysis: Prisma.JsonNull,
  },
  {
    id: "ORD105",
    userId: "user003",
    userEmail: "<EMAIL>",
    planId: "developer",
    vpsPlanName: "Developer VPS", 
    orderDate: new Date("2024-05-29T08:00:00.000Z"),
    status: "FRAUD_REVIEW",
    totalAmount: 1200,
    paymentMethod: "Unknown/Cash",
    ipAddress: "************",
    billingAddress: "123 Fake Street, Nowhere",
    vpsStatus: "SUSPENDED",
    operatingSystem: "Ubuntu 20.04 LTS",
    fraudAnalysis: { 
      isFraudulent: true,
      fraudRiskScore: 0.95,
      riskFactors: [
        "Unverifiable payment method",
        "IP address associated with previous fraud",
        "No order history"
      ],
      recommendation: "Reject",
      analyzedAt: new Date("2024-05-29T08:05:00.000Z").toISOString()
    } as Prisma.InputJsonValue,
  }
];

const paymentsData: Prisma.PaymentCreateManyInput[] = [
  { id: "PAY001", orderId: "ORD103", userId: "user001", paymentDate: new Date("2024-05-26T09:20:00.000Z"), amount: 3200, method: "UPI", transactionId: "UTR1234567890", status: "COMPLETED" },
  { id: "PAY002", orderId: "ORD102", userId: "user001", paymentDate: new Date("2024-05-27T11:35:00.000Z"), amount: 400, method: "UPI", transactionId: "UTR0987654321", status: "PENDING" },
  { id: "PAY003", orderId: "ORD104", userId: "admin001", paymentDate: new Date("2024-05-25T16:50:00.000Z"), amount: 6400, method: "UPI", transactionId: "UTR1122334455", status: "COMPLETED" },
  { id: "PAY004", orderId: "ORD101", userId: "user002", paymentDate: new Date("2024-05-28T10:05:00.000Z"), amount: 1200, method: "UPI", transactionId: "UTR6677889900", status: "COMPLETED" },
  { id: "PAY005", orderId: "ORD105", userId: "user003", paymentDate: new Date("2024-05-29T08:10:00.000Z"), amount: 1200, method: "UPI", transactionId: "UTRABCDEF1234", status: "FAILED" }
];

const initialTicketsData: Prisma.SupportTicketCreateInput[] = [
    { id: "TICKET_INIT_001", userId: "user001", subject: "Initial Test Ticket from JSON", description: "This is a test support ticket loaded from the tickets.json file to demonstrate persistence. I'm having an issue with my first server.", category: "Technical Issue", priority: TicketPriorityEnum.MEDIUM, status: TicketStatusEnum.OPEN, createdAt: new Date("2024-05-20T10:00:00.000Z"), updatedAt: new Date("2024-05-20T10:00:00.000Z"), resolvedAt: null },
    { id: "TICKET_INIT_002", userId: "user002", subject: "Billing question for 2FA User", description: "I have a question regarding my last invoice and how 2FA setup affects it, if at all.", category: "Billing Inquiry", priority: TicketPriorityEnum.LOW, status: TicketStatusEnum.OPEN, createdAt: new Date("2024-05-21T14:30:00.000Z"), updatedAt: new Date("2024-05-21T14:30:00.000Z"), resolvedAt: null },
    { id: "TICKET_HIGH_003", userId: "user001", subject: "Urgent: Server Down, Website Inaccessible!", description: "My main production server (ORD103 - Business VPS) is completely down. None of my websites are accessible. This is critical!", category: "Technical Issue", priority: TicketPriorityEnum.URGENT, status: TicketStatusEnum.CLOSED, createdAt: new Date("2024-05-22T08:15:00.000Z"), updatedAt: new Date("2025-05-21T13:51:47.991Z"), resolvedAt: new Date("2025-05-21T13:51:47.991Z") },
];

const activityLogData: Prisma.ActivityLogEntryCreateInput[] = [
  { id: "ACT001", userId: "user001", timestamp: new Date("2024-05-30T10:00:00.000Z"), type: ActivityType.ORDER_PLACED, description: "Order ORD103 for Business VPS placed.", icon: "ShoppingBag" },
  { id: "ACT002", userId: "user001", timestamp: new Date("2024-05-30T10:05:00.000Z"), type: ActivityType.PAYMENT_COMPLETED, description: "Payment PAY001 for order ORD103 completed.", icon: "CreditCard" },
  { id: "ACT003", userId: "user001", timestamp: new Date("2024-05-29T14:30:00.000Z"), type: ActivityType.TICKET_CREATED, description: "Support ticket 'TICKET_INIT_001' created: Initial Test Ticket from JSON.", icon: "Ticket" },
];

const homepageSlidesData: Prisma.HomepageSlideCreateInput[] = [
  {
    id: "hero-main",
    titleConfig: { 
      text: "Power Your Vision with SkyHosting",
      element: "h1",
      className: "text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-3 sm:mb-4 md:mb-6",
      style: { "textShadow": "0 2px 4px rgba(0,0,0,0.5)" }
    } as Prisma.InputJsonValue,
    description: "Reliable, scalable, and secure VPS hosting solutions tailored to your needs. Experience unparalleled performance and dedicated support.",
    cta1Text: "Explore Plans", cta1Href: "/vps-offerings", cta1Variant: "secondary",
    cta2Text: "Get Started", cta2Href: "/signup", cta2Variant: "outline",
    backgroundClasses: "bg-gradient-to-r from-blue-500 via-purple-600 to-pink-600",
    contentAlignment: "center",
    order: 1
  },
  {
    id: "offer-developer",
    titleConfig: {
      text: "Developer VPS!",
      element: "h1",
      className: "text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-3 sm:mb-4 md:mb-6",
      style: { "textShadow": "0 2px 4px rgba(0,0,0,0.5)" },
      highlight: {
        text: "20% OFF",
        className: "bg-destructive/80 text-destructive-foreground px-3 py-1 rounded-md inline-block",
        position: "before"
      }
    } as Prisma.InputJsonValue,
    description: "Our popular Developer VPS plan is now 20% off for the first 3 months. Supercharge your projects today!",
    cta1Text: "Claim Offer", cta1Href: "/vps-offerings#developer", cta1Variant: "default",
    cta2Text: "View All Plans", cta2Href: "/vps-offerings", cta2Variant: "outline",
    backgroundClasses: "bg-gradient-to-r from-green-500 via-teal-500 to-cyan-600",
    contentAlignment: "center",
    order: 2
  },
  {
    id: "offer-business",
    titleConfig: {
      text: "Business Pro Plan",
      element: "h1",
      className: "text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-3 sm:mb-4 md:mb-6",
      style: { "textShadow": "0 2px 4px rgba(0,0,0,0.5)" },
      highlight: {
        text: "NEW",
        className: "bg-green-500/80 text-white px-3 py-1 rounded-md inline-block",
        position: "before"
      }
    } as Prisma.InputJsonValue,
    description: "Unleash maximum power with our new Business Pro VPS. Dedicated resources, premium support, and unbeatable uptime.",
    cta1Text: "Learn More", cta1Href: "/vps-offerings#business", cta1Variant: "secondary",
    cta2Text: "Contact Sales", cta2Href: "/support", cta2Variant: "outline",
    backgroundClasses: "bg-gradient-to-r from-orange-500 via-red-500 to-purple-700",
    contentAlignment: "center",
    order: 3
  }
];

const ticketCategoryOptionsData: Prisma.TicketCategoryOptionCreateInput[] = [
    { name: "Technical Issue", order: 0 },
    { name: "Billing Inquiry", order: 1 },
    { name: "Performance Issue", order: 2 },
    { name: "Feature Request", order: 3 },
    { name: "General Question", order: 4 }
];

const ticketPriorityOptionsData: Prisma.TicketPriorityOptionCreateInput[] = [
    { value: TicketPriorityEnum.LOW,    label: "Low",    order: 0 },
    { value: TicketPriorityEnum.MEDIUM, label: "Medium", order: 1 },
    { value: TicketPriorityEnum.HIGH,   label: "High",   order: 2 },
    { value: TicketPriorityEnum.URGENT, label: "Urgent", order: 3 }
];


async function main() {
  console.log('Start seeding ...');

  const mappedUsersData = usersSeedData.map(u => {
    const salt = bcryptjs.genSaltSync(10);
    const hashedPassword = bcryptjs.hashSync(u.passwordRaw, salt);
    return {
      id: u.id,
      name: u.name,
      email: u.email,
      password: hashedPassword,
      role: u.role as UserRole,
      is2FAEnabled: u.is2FAEnabled,
      status: u.status as UserStatus,
    };
  });
  await prisma.user.createMany({
    data: mappedUsersData,
    skipDuplicates: true,
  });
  console.log(`${mappedUsersData.length} users seeded or already exist.`);

  await prisma.vPSPlan.createMany({
    data: vpsPlansData,
    skipDuplicates: true,
  });
  console.log(`${vpsPlansData.length} VPS plans seeded or already exist.`);

  await prisma.appTheme.createMany({
    data: themesData,
    skipDuplicates: true,
  });
  console.log(`${themesData.length} themes seeded or already exist.`);

  const themeExists = await prisma.appTheme.findUnique({ where: { id: siteSettingsData.activeThemeId } });
  if (themeExists) {
    await prisma.siteSettings.upsert({
      where: { id: siteSettingsData.id },
      update: { activeThemeId: siteSettingsData.activeThemeId },
      create: siteSettingsData,
    });
    console.log(`Site settings seeded/updated with activeThemeId: ${siteSettingsData.activeThemeId}.`);
  } else {
    const fallbackThemeId = "default";
    await prisma.siteSettings.upsert({
      where: { id: siteSettingsData.id },
      update: { activeThemeId: fallbackThemeId },
      create: { ...siteSettingsData, activeThemeId: fallbackThemeId },
    });
    console.warn(`Configured default theme "${siteSettingsData.activeThemeId}" not found. Site settings seeded with fallback theme: ${fallbackThemeId}.`);
  }

  await prisma.order.createMany({
    data: ordersData.map(order => ({
        ...order,
        status: order.status as OrderStatus, 
        vpsStatus: order.vpsStatus as VpsStatus, 
        fraudAnalysis: order.fraudAnalysis === null ? undefined : order.fraudAnalysis,
    })),
    skipDuplicates: true,
  });
  console.log(`${ordersData.length} orders seeded or already exist.`);

  await prisma.payment.createMany({
    data: paymentsData.map(p => ({...p, status: p.status as PaymentStatus})),
    skipDuplicates: true,
  });
  console.log(`${paymentsData.length} payments seeded or already exist.`);

  const ticketsToSeed: Prisma.SupportTicketCreateInput[] = [...initialTicketsData];
  const priorities = Object.values(TicketPriorityEnum);
  const statuses = Object.values(TicketStatusEnum);
  const categoriesForLoop = ["Technical Issue", "Billing Inquiry", "Performance Issue", "Feature Request", "General Question"];
  const userIdsForTickets = mappedUsersData.length > 0 ? mappedUsersData.map(u => u.id!) : ["user001"]; 

  let currentTicketsCount = ticketsToSeed.length;
  if (currentTicketsCount < 20 && userIdsForTickets.length > 0) {
    for (let i = currentTicketsCount; i < 20; i++) { 
      ticketsToSeed.push({
        userId: userIdsForTickets[i % userIdsForTickets.length], 
        subject: `Mock Ticket Subject ${i + 1}`,
        description: `This is a mock description for ticket number ${i + 1}. It is generated to populate the database for testing purposes.`,
        category: categoriesForLoop[i % categoriesForLoop.length],
        priority: priorities[i % priorities.length],
        status: statuses[i % statuses.length],
        createdAt: new Date(Date.now() - (86400000 * (20 - i))),
        updatedAt: new Date(Date.now() - (3600000 * (20 - i))),
        resolvedAt: (statuses[i % statuses.length] === TicketStatusEnum.RESOLVED || statuses[i % statuses.length] === TicketStatusEnum.CLOSED) ? new Date() : null,
      });
    }
  }
  await prisma.supportTicket.createMany({
    data: ticketsToSeed,
    skipDuplicates: true, 
  });
  console.log(`${ticketsToSeed.length} tickets seeded or duplicates skipped.`);

  const sessionsData: Prisma.SessionCreateManyInput[]  = [
  ];
  if (sessionsData.length > 0) {
    await prisma.session.createMany({
      data: sessionsData,
      skipDuplicates: true,
    });
    console.log(`${sessionsData.length} sessions seeded or already exist.`);
  } else {
    console.log('No sessions pre-seeded (sessionsData array is empty).');
  }

  await prisma.activityLogEntry.createMany({
    data: activityLogData.map(log => ({...log, type: log.type as ActivityType})),
    skipDuplicates: true,
  });
  console.log(`${activityLogData.length} activity log entries seeded or already exist.`);

  await prisma.homepageSlide.createMany({
    data: homepageSlidesData,
    skipDuplicates: true,
  });
  console.log(`${homepageSlidesData.length} homepage slides seeded or already exist.`);

  await prisma.ticketCategoryOption.createMany({
    data: ticketCategoryOptionsData,
    skipDuplicates: true,
  });
  console.log(`${ticketCategoryOptionsData.length} ticket category options seeded or already exist.`);

  await prisma.ticketPriorityOption.createMany({
    data: ticketPriorityOptionsData,
    skipDuplicates: true,
  });
  console.log(`${ticketPriorityOptionsData.length} ticket priority options seeded or already exist.`);


  console.log('Seeding finished.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
