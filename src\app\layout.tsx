
"use client";

import type { ReactNode } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import './globals.css';
import { Navbar } from '@/components/layout/Navbar';
import { Footer } from '@/components/layout/Footer';
import { AuthProvider } from '@/contexts/AuthContext';
import { useAuth } from '@/hooks/useAuth'; 
import { ThemeProvider } from '@/contexts/ThemeContext';
// SidebarProvider and useSidebar are no longer needed
// AdminSidebar and DashboardSidebar are no longer needed
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Toaster } from "@/components/ui/toaster";
import React from 'react';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

// ConditionalSidebarRenderer is removed

function MainContentWrapper({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  const { isAuthenticated, isAdmin } = useAuth();

  // Determine if the current route is an authenticated area that should have a full-width layout
  // allowing child layouts (AdminLayout, DashboardLayout) to control their own padding.
  const isAuthAreaRoute = (pathname.startsWith('/admin') && isAuthenticated && isAdmin) || 
                          (pathname.startsWith('/dashboard') && isAuthenticated);
  
  return (
    <div className="flex flex-col flex-1"> {/* Removed sidebar-related margin classes */}
      <Navbar />
      <main className={cn(
        "flex-grow py-8",
        // Apply container styles only if NOT in an authenticated area that manages its own layout
        !isAuthAreaRoute && "container mx-auto max-w-screen-2xl px-4 sm:px-6 lg:px-8"
      )}>
        {children}
      </main>
      <Footer />
    </div>
  );
}


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased flex flex-col min-h-screen bg-background text-foreground`}>
        <ThemeProvider>
          <AuthProvider>
            {/* SidebarProvider removed */}
            {/* ConditionalSidebarRenderer removed */}
            <MainContentWrapper>
              {children}
            </MainContentWrapper>
            <Toaster />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}

