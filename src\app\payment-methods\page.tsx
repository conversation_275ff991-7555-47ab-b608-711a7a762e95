
"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { CreditCard, Smartphone, Landmark, Loader2 } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default function PaymentMethodsPage() {
  const { isAuthenticated, isLoadingAuth } = useAuth(); // Added isLoadingAuth
  const router = useRouter();

  useEffect(() => {
    if (!isLoadingAuth) { // Only check after auth state is determined
      if (isAuthenticated === false) { 
        router.push('/login?redirect=/payment-methods');
      }
    }
  }, [isAuthenticated, router, isLoadingAuth]);

  if (isLoadingAuth) { 
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-2 text-lg text-muted-foreground">Loading...</p>
      </div>
    );
  }

  if (isAuthenticated === false) { 
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)]">
         <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-lg text-muted-foreground my-4">
          Redirecting to login...
        </p>
        <Button asChild><Link href="/login?redirect=/payment-methods">Go to Login</Link></Button>
      </div>
    );
  }

  return (
    <div className="space-y-12">
      <section className="text-center py-12">
        <h1 className="text-4xl md:text-5xl font-bold text-primary mb-4">Accepted Payment Methods</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          We offer a variety of secure and convenient payment options for your SkyHosting services.
        </p>
      </section>

      <section className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <Card className="shadow-md hover:shadow-lg transition-shadow opacity-70">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xl font-medium text-primary">Credit/Debit Cards</CardTitle>
            <CreditCard className="h-6 w-6 text-accent" />
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              We will soon accept major credit and debit cards including Visa, MasterCard, American Express, and Discover. (Coming Soon)
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-md hover:shadow-lg transition-shadow border-2 border-accent">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xl font-medium text-primary">UPI Payments</CardTitle>
            <Smartphone className="h-6 w-6 text-accent" />
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Pay easily using any UPI-enabled app. Quick, secure, and convenient for our customers in India. Use QR codes or unique payment IDs.
            </p>
            <p className="mt-2 text-sm text-accent hover:underline">
              <Link href="/payment/upi">Go to UPI Payment Page</Link>
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-md hover:shadow-lg transition-shadow opacity-70">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xl font-medium text-primary">Net Banking</CardTitle>
            <Landmark className="h-6 w-6 text-accent" />
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Securely pay directly from your bank account using Net Banking. Supported for most major banks. (Coming Soon)
            </p>
          </CardContent>
        </Card>
      </section>

      <section className="py-12 mt-8 text-center">
        <h2 className="text-2xl font-semibold text-primary mb-4">Payment Security</h2>
        <p className="text-muted-foreground max-w-xl mx-auto">
          All UPI transactions are processed securely. Your financial information is handled by your UPI app and bank.
        </p>
      </section>
    </div>
  );
}
