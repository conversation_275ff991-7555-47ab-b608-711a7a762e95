
"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button, buttonVariants } from '@/components/ui/button'; 
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Users, MoreVertical, Edit3, ToggleLeft, ToggleRight, ShieldCheck, CheckCircle, AlertTriangle, UserX, Loader2, Save } from "lucide-react";
import type { User } from "@/types"; 
import { useToast } from '@/hooks/use-toast';
import { getAllUsers, updateUserStatus, updateUserRole, deleteUserInJson, updateUser } from '@/lib/actions/userManagementActions';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Skeleton } from '@/components/ui/skeleton';

interface ManagedUser extends User {
  joinedDate?: string; 
}

const editUserFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }).max(50, { message: "Name cannot exceed 50 characters."}),
  email: z.string().email({ message: "Please enter a valid email address." }),
});

type EditUserFormData = z.infer<typeof editUserFormSchema>;

export default function UserManagementPage() {
  const [users, setUsers] = useState<ManagedUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditUserDialogOpen, setIsEditUserDialogOpen] = useState(false);
  const [currentUserToEdit, setCurrentUserToEdit] = useState<ManagedUser | null>(null);
  const [userToDelete, setUserToDelete] = useState<ManagedUser | null>(null);
  const { toast } = useToast();

  const editForm = useForm<EditUserFormData>({
    resolver: zodResolver(editUserFormSchema),
    defaultValues: { name: "", email: "" },
  });

  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      const fetchedUsers = await getAllUsers();
      setUsers(fetchedUsers.map(u => ({...u, joinedDate: new Date().toISOString() }))); 
    } catch (error) {
      toast({
        title: "Error fetching users",
        description: error instanceof Error ? error.message : "Could not load user data.",
        variant: "error",
      });
      setUsers([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const handleOpenEditDialog = (user: ManagedUser) => {
    setCurrentUserToEdit(user);
    editForm.reset({
      name: user.name || "",
      email: user.email,
    });
    setIsEditUserDialogOpen(true);
  };

  const onEditUserSubmit = async (data: EditUserFormData) => {
    if (!currentUserToEdit) return;
    
    const result = await updateUser(currentUserToEdit.id, { name: data.name, email: data.email });

    if (result.error || !result.user) {
      toast({ title: "Update Failed", description: result.error || "Could not update user details.", variant: "error" });
    } else {
      toast({ title: "User Updated", description: `Details for ${result.user.name || result.user.email} have been updated.`, variant: "success" });
      setIsEditUserDialogOpen(false);
      fetchUsers(); 
    }
  };


  const handleToggleStatus = async (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    const newStatus = user.status === 'active' ? 'suspended' : 'active';
    const result = await updateUserStatus(userId, newStatus);

    if (result.error) {
      toast({ title: "Update Failed", description: result.error, variant: "error" });
    } else {
      toast({
        title: `User ${newStatus === 'active' ? 'Unsuspended' : 'Suspended'}`,
        description: `${result.user?.name || result.user?.email} is now ${newStatus}.`,
        variant: "success",
      });
      fetchUsers(); 
    }
  };

  const handleMakeAdmin = async (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (!user || user.role === 'admin') return;

    const result = await updateUserRole(userId, 'admin');
    if (result.error) {
      toast({ title: "Promotion Failed", description: result.error, variant: "error" });
    } else {
      toast({
          title: `User Promoted`,
          description: `${result.user?.name || result.user?.email} is now an Admin.`,
          variant: "success",
      });
      fetchUsers(); 
    }
  };
  
  const confirmDeleteUser = async () => {
     if (!userToDelete) return;

     const result = await deleteUserInJson(userToDelete.id);
      if (result.error) {
        toast({ title: `Deletion Failed`, description: result.error, variant: 'error'});
      } else {
        toast({
          title: `User Deleted`,
          description: `${userToDelete.name || userToDelete.email || userToDelete.id} has been removed.`,
          variant: 'success'
        });
        fetchUsers(); 
     }
     setUserToDelete(null); 
  };


  const getStatusBadgeVariant = (status?: User['status']) => {
    switch (status) {
      case 'active': return 'default'; 
      case 'suspended': return 'destructive'; 
      case 'pending_verification': return 'secondary'; 
      default: return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <Users className="h-8 w-8 text-primary" />
        <h1 className="text-3xl font-bold text-primary">User Management</h1>
      </div>
      
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle>All Users</CardTitle>
          <CardDescription>
            View, manage, and edit user accounts on SkyHosting. User data is sourced from users.json.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead><Skeleton className="h-4 w-16" /></TableHead>
                  <TableHead><Skeleton className="h-4 w-24" /></TableHead>
                  <TableHead><Skeleton className="h-4 w-32" /></TableHead>
                  <TableHead><Skeleton className="h-4 w-12" /></TableHead>
                  <TableHead><Skeleton className="h-4 w-20" /></TableHead>
                  <TableHead><Skeleton className="h-4 w-8" /></TableHead>
                  <TableHead className="text-right"><Skeleton className="h-4 w-10 float-right" /></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {[...Array(5)].map((_, i) => (
                  <TableRow key={i}>
                    <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-40" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    <TableCell><Skeleton className="h-5 w-5 rounded-full" /></TableCell>
                    <TableCell className="text-right"><Skeleton className="h-8 w-8 float-right rounded-sm" /></TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : users.length === 0 ? (
            <div className="text-center py-12">
              <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold text-primary mb-2">No Users Found</h3>
              <p className="text-muted-foreground">
                There are currently no registered users in the system.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User ID</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>2FA</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-mono text-xs">{user.id}</TableCell>
                    <TableCell className="font-medium">{user.name || '-'}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Badge variant={user.role === 'admin' ? 'outline' : 'secondary'} className="capitalize">
                        {user.role === 'admin' && <ShieldCheck className="mr-1 h-3 w-3" />}
                        {user.role || 'user'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(user.status)} className="capitalize">
                        {user.status?.replace('_', ' ') || 'N/A'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                        {user.is2FAEnabled 
                            ? <CheckCircle className="h-5 w-5 text-green-500" title="2FA Enabled" /> 
                            : <AlertTriangle className="h-5 w-5 text-yellow-500" title="2FA Disabled" />
                        }
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleOpenEditDialog(user)}>
                            <Edit3 className="mr-2 h-4 w-4" /> Edit User
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleToggleStatus(user.id)}>
                            {user.status === 'active' ? (
                              <><ToggleLeft className="mr-2 h-4 w-4" /> Suspend User</>
                            ) : (
                              <><ToggleRight className="mr-2 h-4 w-4" /> Unsuspend User</>
                            )}
                          </DropdownMenuItem>
                          {user.role !== 'admin' && (
                            <DropdownMenuItem onClick={() => handleMakeAdmin(user.id)}>
                               <ShieldCheck className="mr-2 h-4 w-4" /> Make Admin
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          {!(user.role === 'admin' && users.filter(u => u.role === 'admin').length <= 1) && (
                              <DropdownMenuItem
                                className="text-destructive focus:text-destructive-foreground focus:bg-destructive"
                                onSelect={(e) => { e.preventDefault(); setUserToDelete(user); }}
                              >
                                <UserX className="mr-2 h-4 w-4" /> Delete User
                              </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {currentUserToEdit && (
        <Dialog open={isEditUserDialogOpen} onOpenChange={setIsEditUserDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Edit User: {currentUserToEdit.name || currentUserToEdit.email}</DialogTitle>
              <DialogDescription>
                Make changes to the user's profile. Click save when you're done.
              </DialogDescription>
            </DialogHeader>
            <Form {...editForm}>
              <form onSubmit={editForm.handleSubmit(onEditUserSubmit)} className="grid gap-4 py-4">
                <FormField
                  control={editForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem className="grid grid-cols-4 items-center gap-4">
                      <FormLabel className="text-right col-span-1">Name</FormLabel>
                      <FormControl className="col-span-3">
                        <Input {...field} />
                      </FormControl>
                      <FormMessage className="col-span-4 col-start-2" />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="grid grid-cols-4 items-center gap-4">
                      <FormLabel className="text-right col-span-1">Email</FormLabel>
                      <FormControl className="col-span-3">
                        <Input type="email" {...field} />
                      </FormControl>
                      <FormMessage className="col-span-4 col-start-2" />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setIsEditUserDialogOpen(false)}>Cancel</Button>
                  <Button type="submit" disabled={editForm.formState.isSubmitting}>
                    {editForm.formState.isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                     Save Changes
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      )}

      <AlertDialog open={!!userToDelete} onOpenChange={(isOpen) => { if(!isOpen) setUserToDelete(null);}}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the user account
              for <span className="font-semibold">{userToDelete?.name || userToDelete?.email}</span> and remove their data from the server.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setUserToDelete(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteUser}
              className={buttonVariants({ variant: "destructive" })}
            >
              Delete User
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
