// @ts-nocheck
'use server';

import { analyzeFraudRisk, type AnalyzeFraudRiskInput, type AnalyzeFraudRiskOutput } from "@/ai/flows/analyze-fraud-risk";
import type { Order, OrderStatus, VpsStatus } from "@/types";
import prisma from './prisma';
import { Prisma, ActivityType } from '@prisma/client';

async function getOrderDetailsForFraudAnalysis(orderId: string): Promise<AnalyzeFraudRiskInput | null> {
  if (!orderId) return null;
  const order = await prisma.order.findUnique({
    where: { id: orderId },
    include: {
      plan: true, 
      user: {
        include: {
          orders: { 
            where: { NOT: { id: orderId } }, 
            select: { id: true }
          }
        }
      }
    }
  });

  if (!order || !order.user || !order.plan) {
    console.error(`Order or related user/plan not found for orderId: ${orderId}`);
    return null;
  }

  const vpsPlanName = order.plan.name;
  const userOrderHistory = order.user.orders.map(o => o.id);

  return {
    userData: {
      userId: order.userId,
      email: order.user.email,
      billingAddress: order.billingAddress || "N/A",
      paymentMethod: order.paymentMethod || "N/A",
      orderHistory: userOrderHistory,
    },
    orderData: {
      orderId: order.id,
      vpsPlan: vpsPlanName,
      orderDate: order.orderDate.toISOString(),
      ipAddress: order.ipAddress || "N/A", 
      totalAmount: order.totalAmount.toNumber(), 
    },
  };
}

export async function performFraudCheck(orderId: string): Promise<AnalyzeFraudRiskOutput | { success: false, error: string }> {
  if (!orderId) return { success: false, error: "Order ID is required." };
  try {
    const inputData = await getOrderDetailsForFraudAnalysis(orderId);
    if (!inputData) {
      return { success: false, error: "Order not found or insufficient data for fraud check." };
    }

    const analysisResult = await analyzeFraudRisk(inputData);

    await prisma.order.update({
      where: { id: orderId },
      data: {
        fraudAnalysis: { ...analysisResult, analyzedAt: new Date().toISOString() } as Prisma.InputJsonValue,
      },
    });
    console.log(`Fraud analysis for order ${orderId} saved:`, analysisResult);

    return analysisResult; // This needs to be compatible with AnalyzeFraudRiskOutput if successful
  } catch (error) {
    console.error("Error in performFraudCheck:", error);
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred during fraud analysis.";
    return { success: false, error: errorMessage };
  }
}

export async function getOrdersForFraudReview(): Promise<Order[]> {
  try {
    const ordersFromDb = await prisma.order.findMany({
      include: {
        plan: true, 
      },
      orderBy: { orderDate: 'desc' },
    });

    return ordersFromDb.map(order => ({
      ...order,
      totalAmount: order.totalAmount.toNumber(), 
      ramGB: order.plan.ramGB,
      storageGB: order.plan.storageGB,
      bandwidthTB: order.plan.bandwidthTB,
      pricePerMonth: order.plan.pricePerMonth.toNumber(), 
      originalPricePerMonth: order.plan.originalPricePerMonth?.toNumber(), 
      orderDate: order.orderDate.toISOString(),
      fraudAnalysis: order.fraudAnalysis === Prisma.JsonNull || order.fraudAnalysis === null ? undefined : order.fraudAnalysis as Order['fraudAnalysis'],
      vpsPlanName: order.plan.name,
    }));
  } catch (error) {
    console.error("Error fetching orders for fraud review:", error);
    return [];
  }
}


export async function updateOrderStatus(orderId: string, newStatus: OrderStatus, newVpsStatus?: VpsStatus): Promise<{success: boolean, error?: string, order?: Order}> {
    if (!orderId) return { success: false, error: "Order ID is required." };
    if (!newStatus) return { success: false, error: "New status is required." };
    try {
        const currentOrder = await prisma.order.findUnique({ where: { id: orderId }, include: { plan: true } });
        if (!currentOrder) {
            return { success: false, error: "Order not found." };
        }

        const dataToUpdate: Prisma.OrderUpdateInput = { status: newStatus.toUpperCase() as Prisma.OrderStatus };

        if (newVpsStatus !== undefined) {
            dataToUpdate.vpsStatus = newVpsStatus.toUpperCase() as Prisma.VpsStatus;
        } else {
            if (newStatus.toUpperCase() === 'ACTIVE') {
                 if (!currentOrder.vpsStatus || ['PROVISIONING', 'SUSPENDED', 'STOPPED'].includes(currentOrder.vpsStatus)) {
                    dataToUpdate.vpsStatus = 'RUNNING';
                }
                if (!currentOrder.operatingSystem) {
                    dataToUpdate.operatingSystem = "Ubuntu 22.04 LTS";
                }
                if (!currentOrder.ipAddress) {
                    dataToUpdate.ipAddress = `192.168.1.${Math.floor(100 + Math.random() * 150)}`;
                }
            } else if (newStatus.toUpperCase() === 'CANCELLED' || newStatus.toUpperCase() === 'FRAUD_REVIEW') {
                dataToUpdate.vpsStatus = 'SUSPENDED';
            } else if (newStatus.toUpperCase() === 'PROCESSING' && (currentOrder.status === 'PENDING' || currentOrder.status === 'FRAUD_REVIEW')) {
                dataToUpdate.vpsStatus = 'PROVISIONING';
            }
        }


        const updatedOrderFromDb = await prisma.order.update({
            where: { id: orderId },
            data: dataToUpdate,
            include: { plan: true },
        });

        const returnOrder: Order = {
            ...updatedOrderFromDb,
            orderDate: updatedOrderFromDb.orderDate.toISOString(),
            totalAmount: updatedOrderFromDb.totalAmount.toNumber(), 
            vpsPlanName: updatedOrderFromDb.plan.name,
            ramGB: updatedOrderFromDb.plan.ramGB,
            storageGB: updatedOrderFromDb.plan.storageGB,
            bandwidthTB: updatedOrderFromDb.plan.bandwidthTB,
            pricePerMonth: updatedOrderFromDb.plan.pricePerMonth.toNumber(), 
            originalPricePerMonth: updatedOrderFromDb.plan.originalPricePerMonth?.toNumber(), 
            fraudAnalysis: updatedOrderFromDb.fraudAnalysis === Prisma.JsonNull || updatedOrderFromDb.fraudAnalysis === null ? undefined : updatedOrderFromDb.fraudAnalysis as Order['fraudAnalysis'],
        };

        console.log(`Admin: Order ${orderId} status updated to ${newStatus}. VPS status to ${returnOrder.vpsStatus}.`);
        return { success: true, order: returnOrder };
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to update order status.";
        console.error("Error updating order status (admin):", error);
        return { success: false, error: errorMessage };
    }
}


export async function adminGetAllVpsInstances(): Promise<Order[]> {
  try {
    const ordersFromDb = await prisma.order.findMany({
      include: { plan: true },
      orderBy: { orderDate: 'desc' },
    });

    return ordersFromDb.map(order => ({
      ...order,
      orderDate: order.orderDate.toISOString(),
      totalAmount: order.totalAmount.toNumber(), 
      vpsPlanName: order.plan.name,
      ramGB: order.plan.ramGB,
      storageGB: order.plan.storageGB,
      bandwidthTB: order.plan.bandwidthTB,
      pricePerMonth: order.plan.pricePerMonth.toNumber(), 
      originalPricePerMonth: order.plan.originalPricePerMonth?.toNumber(), 
      fraudAnalysis: order.fraudAnalysis === Prisma.JsonNull || order.fraudAnalysis === null ? undefined : order.fraudAnalysis as Order['fraudAnalysis'],
    }));
  } catch (error) {
    console.error("Error fetching all VPS instances for admin:", error);
    return [];
  }
}

export async function adminUpdateVpsStatus(
  orderId: string,
  newVpsStatus: VpsStatus
): Promise<{ success: boolean; error?: string; order?: Order }> {
  if (!orderId || !newVpsStatus) {
    return { success: false, error: "Order ID and new VPS status are required." };
  }
  try {
    const currentOrder = await prisma.order.findUnique({
      where: { id: orderId },
      include: { plan: true }
    });

    if (!currentOrder) {
      return { success: false, error: 'Order not found.' };
    }

    const dataToUpdate: Prisma.OrderUpdateInput = { vpsStatus: newVpsStatus.toUpperCase() as Prisma.VpsStatus };

    if (currentOrder.vpsStatus === 'SUSPENDED' && (newVpsStatus.toUpperCase() === 'RUNNING' || newVpsStatus.toUpperCase() === 'STOPPED')) {
        if (currentOrder.status === 'FRAUD_REVIEW' || currentOrder.status === 'CANCELLED') {
           dataToUpdate.status = 'ACTIVE'; 
        }
    }

    const updatedOrderFromDb = await prisma.order.update({
        where: { id: orderId },
        data: dataToUpdate,
        include: { plan: true }, 
    });

    if (newVpsStatus.toUpperCase() === 'REBOOTING') {
      setTimeout(async () => {
        try {
            const orderAfterReboot = await prisma.order.findUnique({ where: { id: orderId } });
            if (orderAfterReboot && orderAfterReboot.vpsStatus === 'REBOOTING') {
                await prisma.order.update({
                    where: { id: orderId },
                    data: { vpsStatus: 'RUNNING' },
                });
                console.log(`Admin: VPS ${orderId} rebooted and set to running.`);
            }
        } catch (rebootError) {
             console.error(`Error setting VPS ${orderId} to running after admin reboot:`, rebootError);
        }
      }, 3000); 
    }

    const returnOrder: Order = {
        ...updatedOrderFromDb,
        orderDate: updatedOrderFromDb.orderDate.toISOString(),
        totalAmount: updatedOrderFromDb.totalAmount.toNumber(), 
        vpsPlanName: updatedOrderFromDb.plan.name,
        ramGB: updatedOrderFromDb.plan.ramGB,
        storageGB: updatedOrderFromDb.plan.storageGB,
        bandwidthTB: updatedOrderFromDb.plan.bandwidthTB,
        pricePerMonth: updatedOrderFromDb.plan.pricePerMonth.toNumber(), 
        originalPricePerMonth: updatedOrderFromDb.plan.originalPricePerMonth?.toNumber(), 
        fraudAnalysis: updatedOrderFromDb.fraudAnalysis === Prisma.JsonNull || updatedOrderFromDb.fraudAnalysis === null ? undefined : updatedOrderFromDb.fraudAnalysis as Order['fraudAnalysis'],
    };

    return { success: true, order: returnOrder };
  } catch (error) {
    console.error('Error updating VPS status by admin:', error);
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
      return { success: false, error: 'Order not found for VPS status update.' };
    }
    return { success: false, error: 'Failed to update VPS status.' };
  }
}
