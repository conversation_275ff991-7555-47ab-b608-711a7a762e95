// @ts-nocheck
'use server';

import { Prisma, ActivityType } from '@prisma/client';
import type { Order, OrderStatus, VpsStatus } from '@/types';
import prisma from './prisma';
import { logActivity } from './activityLogActions';
// Import Prisma enums for type casting if needed, but direct string assignment is often fine if strings match enum values
// import type { UserRole, UserStatus, PaymentStatus, TicketStatusEnum, TicketPriorityEnum, VpsPlanSpecialTag } from '@prisma/client';


export async function createOrder(userId: string, planId: string, userEmail: string | undefined): Promise<{ success: boolean, order?: Order; error?: string }> {
  if (!userId || !planId) {
    return { success: false, error: "User ID and Plan ID are required to create an order." };
  }
  try {
    const plan = await prisma.vPSPlan.findUniqueOrThrow({
      where: { id: planId },
    });

    const now = new Date();

    const newOrderFromDb = await prisma.order.create({
      data: {
        userId: userId,
        userEmail: userEmail, 
        planId: planId,
        vpsPlanName: plan.name, 
        orderDate: now,
        status: 'PENDING', 
        totalAmount: plan.pricePerMonth, 
        paymentMethod: 'UPI', 
        vpsStatus: 'PROVISIONING', 
      },
      include: {
        plan: true, 
      },
    });

    await logActivity(
      userId,
      ActivityType.ORDER_PLACED, 
      `Order ${newOrderFromDb.id} for ${newOrderFromDb.plan.name} placed.`,
      'ShoppingBag'
    );

    const returnOrder: Order = {
        ...newOrderFromDb,
        orderDate: newOrderFromDb.orderDate.toISOString(),
        totalAmount: newOrderFromDb.totalAmount.toNumber(), 
        ramGB: newOrderFromDb.plan.ramGB,
        storageGB: newOrderFromDb.plan.storageGB,
        bandwidthTB: newOrderFromDb.plan.bandwidthTB,
        pricePerMonth: newOrderFromDb.plan.pricePerMonth.toNumber(), 
        originalPricePerMonth: newOrderFromDb.plan.originalPricePerMonth?.toNumber(), 
        fraudAnalysis: newOrderFromDb.fraudAnalysis === Prisma.JsonNull || newOrderFromDb.fraudAnalysis === null ? undefined : newOrderFromDb.fraudAnalysis as Order['fraudAnalysis'],
    };

    return { success: true, order: returnOrder };

  } catch (error) {
    console.error("Error creating order:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to create order.";
    return { success: false, error: errorMessage };
  }
}

export async function getUserOrders(userId: string): Promise<Order[]> {
  if (!userId) {
    console.warn('getUserOrders called without a userId.');
    return [];
  }
  try {
    const userOrdersFromDb = await prisma.order.findMany({
      where: { userId },
      include: {
        plan: true, 
      },
      orderBy: { orderDate: 'desc' },
    });

    return userOrdersFromDb.map(order => ({
      ...order,
      orderDate: order.orderDate.toISOString(),
      totalAmount: order.totalAmount.toNumber(),
      vpsPlanName: order.plan.name, 
      ramGB: order.plan.ramGB,
      storageGB: order.plan.storageGB,
      bandwidthTB: order.plan.bandwidthTB,
      pricePerMonth: order.plan.pricePerMonth.toNumber(),
      originalPricePerMonth: order.plan.originalPricePerMonth?.toNumber(),
      fraudAnalysis: order.fraudAnalysis === Prisma.JsonNull || order.fraudAnalysis === null ? undefined : order.fraudAnalysis as Order['fraudAnalysis'],
    }));

  } catch (error) {
    console.error(`Error fetching orders for user ${userId}:`, error);
    return []; // Return empty array on error, client can show a message
  }
}

export async function updateOrderStatus(orderId: string, newStatus: OrderStatus, newVpsStatus?: VpsStatus): Promise<{success: boolean, error?: string, order?: Order}> {
    if (!orderId) return { success: false, error: "Order ID is required." };
    if (!newStatus) return { success: false, error: "New status is required." };

    try {
        const currentOrder = await prisma.order.findUnique({ where: { id: orderId }, include: { plan: true } });
        if (!currentOrder) {
            return { success: false, error: "Order not found." };
        }

        const dataToUpdate: Prisma.OrderUpdateInput = { status: newStatus };

        if (newVpsStatus !== undefined) {
          dataToUpdate.vpsStatus = newVpsStatus;
        } else {
             if (newStatus === 'ACTIVE') {
                if (!currentOrder.vpsStatus || ['PROVISIONING', 'SUSPENDED', 'STOPPED'].includes(currentOrder.vpsStatus)) {
                    dataToUpdate.vpsStatus = 'RUNNING';
                }
                if (!currentOrder.operatingSystem) {
                    dataToUpdate.operatingSystem = "Ubuntu 22.04 LTS";
                }
                if (!currentOrder.ipAddress) {
                    dataToUpdate.ipAddress = `192.168.1.${Math.floor(100 + Math.random() * 150)}`;
                }
            } else if (newStatus === 'CANCELLED' || newStatus === 'FRAUD_REVIEW') {
               dataToUpdate.vpsStatus = 'SUSPENDED';
            } else if (newStatus === 'PROCESSING' && (currentOrder.status === 'PENDING' || currentOrder.status === 'FRAUD_REVIEW')) {
                dataToUpdate.vpsStatus = 'PROVISIONING';
            }
        }


        const updatedOrderFromDb = await prisma.order.update({
            where: { id: orderId },
            data: dataToUpdate,
            include: { plan: true },
        });

        const returnOrder: Order = {
            ...updatedOrderFromDb,
            orderDate: updatedOrderFromDb.orderDate.toISOString(),
            totalAmount: updatedOrderFromDb.totalAmount.toNumber(),
            vpsPlanName: updatedOrderFromDb.plan.name,
            ramGB: updatedOrderFromDb.plan.ramGB,
            storageGB: updatedOrderFromDb.plan.storageGB,
            bandwidthTB: updatedOrderFromDb.plan.bandwidthTB,
            pricePerMonth: updatedOrderFromDb.plan.pricePerMonth.toNumber(),
            originalPricePerMonth: updatedOrderFromDb.plan.originalPricePerMonth?.toNumber(),
            fraudAnalysis: updatedOrderFromDb.fraudAnalysis === Prisma.JsonNull || updatedOrderFromDb.fraudAnalysis === null ? undefined : updatedOrderFromDb.fraudAnalysis as Order['fraudAnalysis'],
        };

        console.log(`Order ${orderId} status updated to ${newStatus}. VPS status: ${returnOrder.vpsStatus}`);
        return { success: true, order: returnOrder };
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Failed to update order status.";
        console.error("Error updating order status:", error);
        return { success: false, error: errorMessage };
    }
}

export async function countUserOrdersByStatus(userId: string, status: OrderStatus): Promise<number> {
  if (!userId) {
    return 0;
  }
  if (!status) {
    console.warn("countUserOrdersByStatus called without status");
    return 0;
  }
  try {
    return await prisma.order.count({
      where: { userId, status: status.toUpperCase() as Prisma.OrderStatus },
    });
  } catch (error) {
    console.error(`Error counting orders for user ${userId} with status ${status}:`, error);
    return 0;
  }
}

export async function updateUserVpsStatus(
  orderId: string,
  newVpsStatus: VpsStatus,
  userId: string
): Promise<{ success: boolean; error?: string; order?: Order }> {
  if (!orderId || !userId || !newVpsStatus) {
    return { success: false, error: "Order ID, User ID, and new VPS status are required." };
  }

  try {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: { plan: true },
    });

    if (!order) {
      return { success: false, error: 'Order not found.' };
    }

    if (order.userId !== userId) {
      return { success: false, error: 'User not authorized to modify this VPS.' };
    }
    
    if (order.status !== 'ACTIVE' && newVpsStatus !== 'SUSPENDED') { // Allow suspending non-active orders
        return { success: false, error: 'VPS actions (except suspend) only allowed for active services.' };
    }
    
    const oldVpsStatus = order.vpsStatus;
    
    const updatedOrderFromDb = await prisma.order.update({
        where: { id: orderId },
        data: { vpsStatus: newVpsStatus.toUpperCase() as Prisma.VpsStatus },
        include: { plan: true },
    });

    if (newVpsStatus.toUpperCase() === 'REBOOTING') {
      setTimeout(async () => {
        try {
            const currentOrderAfterReboot = await prisma.order.findUnique({ where: { id: orderId } });
            if (currentOrderAfterReboot && currentOrderAfterReboot.vpsStatus === 'REBOOTING') {
                 await prisma.order.update({
                    where: { id: orderId },
                    data: { vpsStatus: 'RUNNING' },
                });
                console.log(`VPS ${orderId} rebooted and set to running.`);
                 await logActivity(
                    userId,
                    ActivityType.VPS_STARTED, 
                    `VPS for order ${orderId} finished rebooting and is now running.`,
                    'Play'
                );
            }
        } catch (rebootError) {
            console.error(`Error setting VPS ${orderId} to running after reboot:`, rebootError);
        }
      }, 3000); 
    }

    if (newVpsStatus.toUpperCase() !== 'REBOOTING' && oldVpsStatus !== newVpsStatus.toUpperCase()) {
      let activityType: ActivityType | undefined = undefined;
      let activityIcon: 'Play' | 'PowerOff' | undefined = undefined; 
      
      if (newVpsStatus.toUpperCase() === 'RUNNING') {
        activityType = ActivityType.VPS_STARTED;
        activityIcon = 'Play';
      } else if (newVpsStatus.toUpperCase() === 'STOPPED' || newVpsStatus.toUpperCase() === 'SUSPENDED') {
        activityType = ActivityType.VPS_STOPPED;
        activityIcon = 'PowerOff';
      }

      if (activityType) {
        await logActivity(
          userId,
          activityType,
          `VPS for order ${orderId} status changed to ${newVpsStatus.toUpperCase()}.`,
          activityIcon
        );
      }
    }
    
    const returnOrder: Order = {
        ...updatedOrderFromDb,
        orderDate: updatedOrderFromDb.orderDate.toISOString(),
        totalAmount: updatedOrderFromDb.totalAmount.toNumber(),
        vpsPlanName: updatedOrderFromDb.plan.name,
        ramGB: updatedOrderFromDb.plan.ramGB,
        storageGB: updatedOrderFromDb.plan.storageGB,
        bandwidthTB: updatedOrderFromDb.plan.bandwidthTB,
        pricePerMonth: updatedOrderFromDb.plan.pricePerMonth.toNumber(),
        originalPricePerMonth: updatedOrderFromDb.plan.originalPricePerMonth?.toNumber(),
        fraudAnalysis: updatedOrderFromDb.fraudAnalysis === Prisma.JsonNull || updatedOrderFromDb.fraudAnalysis === null ? undefined : updatedOrderFromDb.fraudAnalysis as Order['fraudAnalysis'],
    };

    return { success: true, order: returnOrder };
  } catch (error) {
    console.error('Error updating VPS status:', error);
    return { success: false, error: 'Failed to update VPS status.' };
  }
}
