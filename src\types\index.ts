
export interface User {
  id: string;
  email: string;
  name?: string;
  is2FAEnabled?: boolean;
  role?: 'ADMIN' | 'USER'; // Changed to uppercase
  status?: 'ACTIVE' | 'SUSPENDED' | 'PENDING_VERIFICATION'; // Changed to uppercase
}

// Renamed to avoid conflict with DOM Session type if ever used.
export interface AppSession {
  sessionId: string;
  userId: string;
  expiresAt: string; // ISO Date string
  userAgent?: string;
  ipAddress?: string;
}

export interface VPSPlan {
  id: string;
  name: string;
  description: string;
  pricePerMonth: number;
  originalPricePerMonth?: number;
  discountLabel?: string;
  specialTag?: 'MOST_BOUGHT' | 'BEST_PRICE' | 'NEW' | 'RECOMMENDED' | 'SALE';
  cpuCores: number;
  ramGB: number; // Can be float like 0.5
  storageGB: number;
  bandwidthTB: number; // Can be float
  features: string[];
}

export interface FraudRiskAnalysisInput {
  userData: {
    userId: string;
    email: string;
    billingAddress: string;
    paymentMethod: string;
    orderHistory?: string[];
  };
  orderData: {
    orderId: string;
    vpsPlan: string;
    orderDate: string;
    ipAddress: string;
    totalAmount: number;
  };
}

export interface FraudRiskAnalysisOutput {
  isFraudulent: boolean;
  fraudRiskScore: number;
  riskFactors: string[];
  recommendation: string;
}


export interface Order {
  id: string;
  userId: string;
  userEmail?: string;
  planId: string;
  vpsPlanName?: string; // Denormalized for easier display
  orderDate: string; // ISO Date string
  status: 'PENDING' | 'ACTIVE' | 'CANCELLED' | 'FRAUD_REVIEW' | 'PROCESSING';
  totalAmount: number;
  paymentMethod: string;
  billingAddress?: string;
  ipAddress?: string;
  fraudAnalysis?: FraudRiskAnalysisOutput & { analyzedAt?: string };
  vpsStatus?: 'RUNNING' | 'STOPPED' | 'REBOOTING' | 'SUSPENDED' | 'PROVISIONING' | 'ERROR';
  operatingSystem?: string;
}

export interface Payment {
  id: string;
  orderId: string;
  userId: string;
  paymentDate: string; // ISO Date string
  amount: number;
  method: string; // e.g., 'UPI'
  transactionId?: string; // e.g., UTR number for UPI
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
}


// Support Ticket System
export type TicketStatus = 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED';
export type TicketPriority = 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';

export interface SupportTicket {
  id: string;
  userId: string;
  subject: string;
  description: string;
  category: string;
  priority: TicketPriority;
  status: TicketStatus;
  createdAt: string; // ISO Date string
  updatedAt: string; // ISO Date string
  resolvedAt?: string; // ISO Date string
}

// Theme System Types
export interface ThemeSettings {
  background: string;
  foreground: string;
  primary: string;
  primaryForeground: string;
  secondary: string;
  secondaryForeground: string;
  accent: string;
  accentForeground: string;
}

export interface AppTheme {
  id: string;
  name: string;
  settings: ThemeSettings;
}

// Activity Log
export type ActivityLogIcon = 
  | 'ShoppingBag' | 'CreditCard' | 'Ticket' | 'Play' 
  | 'PowerOff' | 'UserCog' | 'LogIn' | 'Bell' | 'ActivityIcon' | 'UserPlus';

export interface ActivityLogEntry {
  id: string;
  userId: string;
  timestamp: string; // ISO Date string
  type: 'ORDER_PLACED' | 'PAYMENT_COMPLETED' | 'TICKET_CREATED' | 'VPS_STARTED' | 'VPS_STOPPED' | 'PROFILE_UPDATED' | 'LOGIN_SUCCESS' | 'SIGNUP_SUCCESS' | 'GENERIC_NOTIFICATION';
  description: string;
  icon?: ActivityLogIcon;
}

// Session type for Prisma (mirrors what's in schema.prisma)
export interface PrismaSession {
  id: string;
  sessionId: string;
  userId: string;
  expiresAt: Date;
  createdAt: Date;
  userAgent?: string | null;
  ipAddress?: string | null;
  user: User; // Relation to User
}
