// This is an auto-generated file from Firebase Studio.

'use server';

/**
 * @fileOverview Analyzes VPS orders for potential fraud using AI.
 *
 * - analyzeFraudRisk - A function that analyzes VPS orders for potential fraud.
 * - AnalyzeFraudRiskInput - The input type for the analyzeFraudRisk function.
 * - AnalyzeFraudRiskOutput - The return type for the analyzeFraudRisk function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const AnalyzeFraudRiskInputSchema = z.object({
  userData: z
    .object({
      userId: z.string().describe('Unique identifier for the user.'),
      email: z.string().email().describe('User email address.'),
      billingAddress: z.string().describe('User billing address.'),
      paymentMethod: z.string().describe('User payment method.'),
      orderHistory: z
        .array(z.string())
        .optional()
        .describe('List of previous order IDs for the user.'),
    })
    .describe('User account data.'),
  orderData: z
    .object({
      orderId: z.string().describe('Unique identifier for the order.'),
      vpsPlan: z.string().describe('The VPS plan the user is ordering.'),
      orderDate: z.string().describe('Date of the order.'),
      ipAddress: z.string().describe('IP address used to place the order.'),
      totalAmount: z.number().describe('Total amount of the order.'),
    })
    .describe('Order details.'),
});

export type AnalyzeFraudRiskInput = z.infer<typeof AnalyzeFraudRiskInputSchema>;

const AnalyzeFraudRiskOutputSchema = z.object({
  isFraudulent: z.boolean().describe('Whether the order is likely fraudulent.'),
  fraudRiskScore: z.number().describe('A score between 0 and 1 indicating the risk of fraud, with 1 being highest risk.'),
  riskFactors: z
    .array(z.string())
    .describe('List of factors contributing to the fraud risk score.'),
  recommendation: z
    .string()
    .describe('Recommendation for handling the order (e.g., approve, review, reject).'),
});

export type AnalyzeFraudRiskOutput = z.infer<typeof AnalyzeFraudRiskOutputSchema>;

export async function analyzeFraudRisk(input: AnalyzeFraudRiskInput): Promise<AnalyzeFraudRiskOutput> {
  return analyzeFraudRiskFlow(input);
}

const prompt = ai.definePrompt({
  name: 'analyzeFraudRiskPrompt',
  input: {schema: AnalyzeFraudRiskInputSchema},
  output: {schema: AnalyzeFraudRiskOutputSchema},
  prompt: `You are an AI fraud detection expert, tasked with analyzing VPS orders for potential fraud.

  Analyze the provided user data and order details to determine the likelihood of fraud.
  Provide a fraud risk score between 0 and 1, where 1 indicates the highest risk.
  Identify the factors contributing to the fraud risk score.
  Provide a recommendation for handling the order (e.g., approve, review, reject).

  User Data:
  {{#each (Object.entries userData) }}
  {{@key}}: {{this.[1]}}
  {{/each}}

  Order Data:
  {{#each (Object.entries orderData) }}
  {{@key}}: {{this.[1]}}
  {{/each}}

  Output in JSON format:
  {
    "isFraudulent": whether the order is likely fraudulent (true or false),
    "fraudRiskScore": a number between 0 and 1 indicating the risk of fraud,
    "riskFactors": ["list of factors contributing to the fraud risk score"],
    "recommendation": "recommendation for handling the order (e.g., approve, review, reject)"
  }`,
});

const analyzeFraudRiskFlow = ai.defineFlow(
  {
    name: 'analyzeFraudRiskFlow',
    inputSchema: AnalyzeFraudRiskInputSchema,
    outputSchema: AnalyzeFraudRiskOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
