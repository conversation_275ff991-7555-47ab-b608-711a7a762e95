-- Create<PERSON>num
CREATE TYPE "user_role" AS ENUM ('USER', 'ADMIN');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "user_status" AS ENUM ('ACTIVE', 'SUSPENDED', 'PENDING_VERIFICATION');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "order_status" AS ENUM ('PENDING', 'ACTIVE', 'CANCELLED', 'FRAUD_REVIEW', 'PROCESSING');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "payment_status" AS ENUM ('PENDING', 'COMPLETED', 'FAILED');

-- CreateEnum
CREATE TYPE "ticket_status" AS ENUM ('OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED');

-- CreateEnum
CREATE TYPE "ticket_priority" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'URGENT');

-- CreateEnum
CREATE TYPE "activity_type" AS ENUM ('ORDER_PLACED', 'PAYMENT_COMPLETED', 'TICKET_CREATED', 'VPS_STARTED', 'VPS_STOPPED', 'PROFILE_UPDATED', 'LOGIN_SUCCESS', 'GENERIC_NOTIFICATION', 'SIGNUP_SUCCESS');

-- CreateEnum
CREATE TYPE "vps_status" AS ENUM ('RUNNING', 'STOPPED', 'REBOOTING', 'SUSPENDED', 'PROVISIONING', 'ERROR');

-- CreateEnum
CREATE TYPE "vps_plan_special_tag" AS ENUM ('MOST_BOUGHT', 'BEST_PRICE', 'NEW', 'RECOMMENDED', 'SALE');

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "name" TEXT,
    "password" TEXT NOT NULL,
    "role" "user_role" NOT NULL DEFAULT 'USER',
    "is2FAEnabled" BOOLEAN DEFAULT false,
    "status" "user_status" NOT NULL DEFAULT 'ACTIVE',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "vps_plans" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "pricePerMonth" DECIMAL(10,2) NOT NULL,
    "originalPricePerMonth" DECIMAL(10,2),
    "discountLabel" TEXT,
    "specialTag" "vps_plan_special_tag",
    "cpuCores" INTEGER NOT NULL,
    "ramGB" DOUBLE PRECISION NOT NULL,
    "storageGB" INTEGER NOT NULL,
    "bandwidthTB" DOUBLE PRECISION NOT NULL,
    "features" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vps_plans_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "app_themes" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "settings_background" TEXT NOT NULL,
    "settings_foreground" TEXT NOT NULL,
    "settings_primary" TEXT NOT NULL,
    "settings_primary_foreground" TEXT NOT NULL,
    "settings_secondary" TEXT NOT NULL,
    "settings_secondary_foreground" TEXT NOT NULL,
    "settings_accent" TEXT NOT NULL,
    "settings_accent_foreground" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "app_themes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "site_settings" (
    "id" TEXT NOT NULL,
    "activeThemeId" TEXT NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "site_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "orders" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "userEmail" TEXT,
    "planId" TEXT NOT NULL,
    "vpsPlanName" TEXT,
    "orderDate" TIMESTAMP(3) NOT NULL,
    "status" "order_status" NOT NULL,
    "totalAmount" DECIMAL(10,2) NOT NULL,
    "paymentMethod" TEXT,
    "billingAddress" TEXT,
    "ipAddress" TEXT,
    "fraudAnalysis" JSONB,
    "vpsStatus" "vps_status",
    "operatingSystem" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "orders_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "payments" (
    "id" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "paymentDate" TIMESTAMP(3) NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "method" TEXT NOT NULL,
    "transactionId" TEXT,
    "status" "payment_status" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "payments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "support_tickets" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "subject" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "priority" "ticket_priority" NOT NULL,
    "status" "ticket_status" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "resolvedAt" TIMESTAMP(3),

    CONSTRAINT "support_tickets_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sessions" (
    "id" TEXT NOT NULL,
    "session_id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expires_at" TIMESTAMP(3) NOT NULL,
    "user_agent" TEXT,
    "ip_address" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "activity_log_entries" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL,
    "type" "activity_type" NOT NULL,
    "description" TEXT NOT NULL,
    "icon" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "activity_log_entries_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "homepage_slides" (
    "id" TEXT NOT NULL,
    "titleConfig" JSONB NOT NULL,
    "description" TEXT NOT NULL,
    "cta1Text" TEXT,
    "cta1Href" TEXT,
    "cta1Variant" TEXT,
    "cta2Text" TEXT,
    "cta2Href" TEXT,
    "cta2Variant" TEXT,
    "backgroundClasses" TEXT,
    "contentAlignment" TEXT,
    "order" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "homepage_slides_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ticket_category_options" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "order" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "ticket_category_options_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ticket_priority_options" (
    "id" TEXT NOT NULL,
    "value" "ticket_priority" NOT NULL,
    "label" TEXT NOT NULL,
    "order" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "ticket_priority_options_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "vps_plans_name_key" ON "vps_plans"("name");

-- CreateIndex
CREATE UNIQUE INDEX "app_themes_name_key" ON "app_themes"("name");

-- CreateIndex
CREATE UNIQUE INDEX "site_settings_activeThemeId_key" ON "site_settings"("activeThemeId");

-- CreateIndex
CREATE UNIQUE INDEX "orders_ipAddress_key" ON "orders"("ipAddress");

-- CreateIndex
CREATE INDEX "orders_userId_idx" ON "orders"("userId");

-- CreateIndex
CREATE INDEX "orders_planId_idx" ON "orders"("planId");

-- CreateIndex
CREATE INDEX "payments_orderId_idx" ON "payments"("orderId");

-- CreateIndex
CREATE INDEX "payments_userId_idx" ON "payments"("userId");

-- CreateIndex
CREATE INDEX "support_tickets_userId_idx" ON "support_tickets"("userId");

-- CreateIndex
CREATE INDEX "support_tickets_status_idx" ON "support_tickets"("status");

-- CreateIndex
CREATE UNIQUE INDEX "sessions_session_id_key" ON "sessions"("session_id");

-- CreateIndex
CREATE INDEX "sessions_userId_idx" ON "sessions"("userId");

-- CreateIndex
CREATE INDEX "activity_log_entries_userId_idx" ON "activity_log_entries"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "ticket_category_options_name_key" ON "ticket_category_options"("name");

-- CreateIndex
CREATE UNIQUE INDEX "ticket_priority_options_value_key" ON "ticket_priority_options"("value");

-- AddForeignKey
ALTER TABLE "site_settings" ADD CONSTRAINT "site_settings_activeThemeId_fkey" FOREIGN KEY ("activeThemeId") REFERENCES "app_themes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "orders" ADD CONSTRAINT "orders_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "orders" ADD CONSTRAINT "orders_planId_fkey" FOREIGN KEY ("planId") REFERENCES "vps_plans"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payments" ADD CONSTRAINT "payments_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payments" ADD CONSTRAINT "payments_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "support_tickets" ADD CONSTRAINT "support_tickets_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activity_log_entries" ADD CONSTRAINT "activity_log_entries_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
