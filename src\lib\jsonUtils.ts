
'use server';

import fs from 'fs/promises';
import path from 'path';
import type { TicketPriority } from '@/types'; // For TicketOptions

// Helper to construct the full file path - NOT EXPORTED
function getDbFilePath(fileName: string): string {
  return path.join(process.cwd(), 'src', 'database', fileName);
}

export async function readJsonFile<T>(jsonFileName: string): Promise<T[]> {
  const filePath = getDbFilePath(jsonFileName);
  try {
    const data = await fs.readFile(filePath, 'utf-8');
    const parsedData = JSON.parse(data);
    // Ensure it's an array, even if it's a single object in a file intended to be an array of one (like site-settings)
    return Array.isArray(parsedData) ? parsedData : [parsedData];
  } catch (error) {
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      console.warn(`JSON file ${jsonFileName} not found. Returning empty array.`);
      if (jsonFileName === 'site-settings.json') { // Specific fallback for site-settings
        return [{ activeThemeId: 'default' }] as unknown as T[];
      }
      return []; 
    }
    console.error(`Failed to read ${jsonFileName}:`, error);
    throw new Error(`Could not load data from ${jsonFileName}.`);
  }
}

export interface TicketOptionsStructure {
  categories: string[];
  priorities: { value: TicketPriority; label: string }[];
}
export async function readTicketOptionsFile(): Promise<TicketOptionsStructure> {
  const filePath = getDbFilePath('ticket-options.json');
  try {
    const data = await fs.readFile(filePath, 'utf-8');
    return JSON.parse(data) as TicketOptionsStructure;
  } catch (error) {
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      console.warn(`JSON file ticket-options.json not found. Returning empty options.`);
      return { categories: [], priorities: [] };
    }
    console.error(`Failed to read ticket-options.json:`, error);
    throw new Error(`Could not load data from ticket-options.json.`);
  }
}

export async function writeJsonFile<T>(jsonFileName: string, data: T[] | T): Promise<void> { 
  const filePath = getDbFilePath(jsonFileName);
  try {
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8');
  } catch (error) {
    console.error(`Failed to write to ${jsonFileName}:`, error);
    throw new Error(`Could not save data to ${jsonFileName}.`);
  }
}
