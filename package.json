{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 9002", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "postinstall": "prisma generate", "prisma:seed": "tsx prisma/seed.ts", "prisma:migrate:dev": "prisma migrate dev", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:generate": "prisma generate"}, "dependencies": {"@genkit-ai/googleai": "^1.14.0", "@genkit-ai/next": "^1.14.0", "@hookform/resolvers": "^4.1.3", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.81.5", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "firebase": "^11.10.0", "genkit": "^1.14.0", "lucide-react": "^0.475.0", "next": "15.3.5", "patch-package": "^8.0.0", "pg": "^8.16.3", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "recharts": "^2.15.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.76"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20.17.50", "@types/react": "^18", "@types/react-dom": "^18", "genkit-cli": "^1.14.0", "postcss": "^8", "prisma": "^6.11.1", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "prisma": {"seed": "tsx prisma/seed.ts"}}