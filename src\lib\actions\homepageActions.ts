
// @ts-nocheck
'use server';

import type { RawSlideData } from '@/components/home/<USER>'; // This type might need adjustment
import prisma from './prisma';
import { Prisma } from '@prisma/client';

// The RawSlideData type from HeroSlideshow might be slightly different
// from what we store in the DB, especially if titleConfig is complex.
// For now, we assume they are compatible or that the client component handles transformation.

export async function getSlideshowData(): Promise<RawSlideData[]> {
  try {
    const slidesFromDb = await prisma.homepageSlide.findMany({
      orderBy: { order: 'asc' },
    });

    // Transform data from DB to match RawSlideData expected by the component
    return slidesFromDb.map(slide => {
      // Ensure titleConfig is an object, even if it's Prisma.JsonNull
      const titleConfig = (typeof slide.titleConfig === 'object' && slide.titleConfig !== null)
        ? slide.titleConfig
        : {};

      return {
        id: slide.id,
        // titleConfig is expected to be an object by the client component.
        // If it's Prisma.JsonNull or not an object, provide a default empty object.
        titleConfig: titleConfig as RawSlideData['titleConfig'], // Assert type
        description: slide.description,
        cta1: (slide.cta1Text && slide.cta1Href) ? { text: slide.cta1Text, href: slide.cta1Href, variant: slide.cta1Variant as any } : undefined,
        cta2: (slide.cta2Text && slide.cta2Href) ? { text: slide.cta2Text, href: slide.cta2Href, variant: slide.cta2Variant as any } : undefined,
        backgroundClasses: slide.backgroundClasses || undefined,
        contentAlignment: slide.contentAlignment as RawSlideData['contentAlignment'] || undefined,
        // order is not directly used by RawSlideData, but was in DB model for sorting
      };
    });
  } catch (error) {
    console.error('Error in getSlideshowData:', error);
    return [];
  }
}
