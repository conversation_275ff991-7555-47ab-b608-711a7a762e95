[{"id": "ORD101", "userId": "user002", "userEmail": "<EMAIL>", "planId": "developer", "vpsPlanName": "Developer VPS", "orderDate": "2024-05-28T10:00:00.000Z", "status": "active", "totalAmount": 1200, "paymentMethod": "UPI", "ipAddress": "*************", "billingAddress": "1 Infinite Loop, Cupertino, CA", "vpsStatus": "running", "operatingSystem": "Ubuntu 22.04 LTS"}, {"id": "ORD102", "userId": "user001", "userEmail": "<EMAIL>", "planId": "starter", "vpsPlanName": "Starter VPS", "orderDate": "2024-05-27T11:30:00.000Z", "status": "pending", "totalAmount": 400, "paymentMethod": "Credit Card", "ipAddress": "*************", "billingAddress": "PO Box 123, Somewhere, XY", "vpsStatus": "provisioning", "operatingSystem": "Debian 12"}, {"id": "ORD103", "userId": "user001", "userEmail": "<EMAIL>", "planId": "business", "vpsPlanName": "Business VPS", "orderDate": "2024-05-26T09:15:00.000Z", "status": "active", "totalAmount": 3200, "paymentMethod": "UPI", "ipAddress": "*************", "billingAddress": "456 Business Rd, Big City, ST", "vpsStatus": "stopped", "operatingSystem": "CentOS Stream 9"}, {"id": "ORD104", "userId": "admin001", "userEmail": "<EMAIL>", "planId": "enterprise", "vpsPlanName": "Enterprise VPS", "orderDate": "2024-05-25T16:45:00.000Z", "status": "active", "totalAmount": 6400, "paymentMethod": "UPI", "ipAddress": "*************", "billingAddress": "789 Corp Plaza, Metroville, ZP", "vpsStatus": "suspended", "operatingSystem": "AlmaLinux 9"}, {"id": "ORD105", "userId": "user003", "userEmail": "<EMAIL>", "planId": "developer", "vpsPlanName": "Developer VPS", "orderDate": "2024-05-29T08:00:00.000Z", "status": "fraud_review", "totalAmount": 1200, "paymentMethod": "Unknown/Cash", "ipAddress": "************", "billingAddress": "123 Fake Street, Nowhere", "fraudAnalysis": {"isFraudulent": true, "fraudRiskScore": 0.95, "riskFactors": ["Unverifiable payment method", "IP address associated with previous fraud", "No order history"], "recommendation": "Reject", "analyzedAt": "2024-05-29T08:05:00.000Z"}, "vpsStatus": "suspended", "operatingSystem": "Ubuntu 20.04 LTS"}, {"id": "ORD1747876666820275", "userId": "user001", "userEmail": "<EMAIL>", "planId": "basic-ssd", "vpsPlanName": "Basic SSD", "orderDate": "2025-05-22T01:17:46.820Z", "status": "pending", "totalAmount": 250, "paymentMethod": "UPI", "vpsStatus": "provisioning"}, {"id": "ORD1747876667481130", "userId": "user001", "userEmail": "<EMAIL>", "planId": "basic-ssd", "vpsPlanName": "Basic SSD", "orderDate": "2025-05-22T01:17:47.481Z", "status": "processing", "totalAmount": 250, "paymentMethod": "UPI", "vpsStatus": "provisioning"}]