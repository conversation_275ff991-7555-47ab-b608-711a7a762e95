
"use client";

import { useTheme, initialDefaultTheme } from "@/contexts/ThemeContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label as ShadcnLabel } from "@/components/ui/label"; 
import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { CheckCircle, Palette, Loader2, Save, Trash2, PlusCircle } from "lucide-react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import type { ThemeSettings, AppTheme } from "@/types";
import { useEffect, useState } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

const hslColorString = z.string().regex(
  /^\d{1,3}\s\d{1,3}%\s\d{1,3}%$/,
  "Must be HSL format (e.g., '210 100% 50%')" 
);

const themeSettingsSchema = z.object({
  background: hslColorString,
  foreground: hslColorString,
  primary: hslColorString,
  primaryForeground: hslColorString,
  secondary: hslColorString,
  secondaryForeground: hslColorString,
  accent: hslColorString,
  accentForeground: hslColorString,
});

const newThemeFormSchema = z.object({
  name: z.string().min(3, { message: "Theme name must be at least 3 characters." }).max(30, { message: "Theme name cannot exceed 30 characters." }),
  settings: themeSettingsSchema,
});


type ThemeSettingsFormData = z.infer<typeof themeSettingsSchema>;
type NewThemeFormData = z.infer<typeof newThemeFormSchema>;

const defaultNewThemeSettings: ThemeSettings = {
  background: "0 0% 100%",
  foreground: "0 0% 3.9%",
  primary: "0 0% 9%",
  primaryForeground: "0 0% 98%",
  secondary: "0 0% 96.1%",
  secondaryForeground: "0 0% 9%",
  accent: "0 0% 9%",
  accentForeground: "0 0% 98%",
};

export default function ThemeSettingsPage() {
  const { 
    theme: currentTheme, 
    setTheme, 
    availableThemes, 
    isLoadingThemes, 
    saveCurrentThemeSettings, 
    deleteTheme,
    addNewTheme 
  } = useTheme();
  const [themeToDelete, setThemeToDelete] = useState<AppTheme | null>(null);
  const [isCreateThemeDialogOpen, setIsCreateThemeDialogOpen] = useState(false);

  const editThemeForm = useForm<ThemeSettingsFormData>({
    resolver: zodResolver(themeSettingsSchema),
    defaultValues: currentTheme.settings,
  });

  const createThemeForm = useForm<NewThemeFormData>({
    resolver: zodResolver(newThemeFormSchema),
    defaultValues: {
      name: "",
      settings: { ...defaultNewThemeSettings }
    },
  });

  useEffect(() => {
    if (currentTheme && currentTheme.settings) {
      editThemeForm.reset(currentTheme.settings);
    }
  }, [currentTheme, editThemeForm]);

  const onSubmitEditThemeSettings = async (data: ThemeSettingsFormData) => {
    editThemeForm.clearErrors(); 
    const result = await saveCurrentThemeSettings(data);

    if (result && result.success === false && result.fieldErrors) {
        (Object.keys(result.fieldErrors) as Array<keyof ThemeSettingsFormData>).forEach((field) => {
          const messages = result.fieldErrors?.[field];
          if (messages && messages.length > 0) {
           editThemeForm.setError(field, { type: 'server', message: messages[0] });
         }
       });
    }
  };

  const onSubmitCreateNewTheme = async (data: NewThemeFormData) => {
    createThemeForm.clearErrors();
    const result = await addNewTheme(data.name, data.settings);
    if (result.success) {
      setIsCreateThemeDialogOpen(false);
      createThemeForm.reset({ name: "", settings: { ...defaultNewThemeSettings } });
    } else {
      if (result.fieldErrors) {
        if (result.fieldErrors.name) {
          createThemeForm.setError("name", { type: 'server', message: result.fieldErrors.name[0] });
        }
        if (result.fieldErrors.settings) {
          (Object.keys(result.fieldErrors.settings) as Array<keyof ThemeSettings>).forEach((settingKey) => {
             const messages = result.fieldErrors?.settings?.[settingKey];
             if (messages && messages.length > 0) {
                createThemeForm.setError(`settings.${settingKey}`, { type: 'server', message: messages[0] });
             }
          });
        }
      }
    }
  };


  const handleApplyPreview = (settingKey: keyof ThemeSettings, value: string, formType: 'edit' | 'create') => {
    if (/^\d{1,3}\s\d{1,3}%\s\d{1,3}%$/.test(value) || value.trim() === "") {
      if (formType === 'edit' && currentTheme.id === initialDefaultTheme.id) {
         // Don't apply preview if editing the "Loading..." theme or directly applying to root from create form.
         // Instead, update the form's preview swatches.
      } else {
        const root = document.documentElement;
        const cssVarName = `--${settingKey.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
        root.style.setProperty(cssVarName, value);
      }
    }
  };

  const handleDeleteConfirm = async () => {
    if (themeToDelete) {
      await deleteTheme(themeToDelete.id);
      setThemeToDelete(null); 
    }
  };


  if (isLoadingThemes) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-3">
          <Skeleton className="h-8 w-8 rounded-full" />
          <Skeleton className="h-8 w-3/4" />
        </div>
        <Card className="shadow-md">
          <CardHeader>
            <Skeleton className="h-6 w-1/2 mb-2" />
            <Skeleton className="h-4 w-3/4" />
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 py-12">
            {[...Array(3)].map((_, i) => (
              <ShadcnLabel key={i} className="flex flex-col items-start rounded-lg border p-4 cursor-wait">
                <div className="flex justify-between w-full items-center mb-3">
                  <Skeleton className="h-6 w-1/2" />
                  <Skeleton className="h-5 w-5 rounded-full" />
                </div>
                <div className="flex space-x-2 mb-3">
                  {[...Array(5)].map((_, j) => (
                    <Skeleton key={j} className="w-8 h-8 rounded-full" />
                  ))}
                </div>
              </ShadcnLabel>
            ))}
          </CardContent>
        </Card>
        <Card className="shadow-md">
          <CardHeader>
            <Skeleton className="h-6 w-1/2 mb-2" />
            <Skeleton className="h-4 w-3/4" />
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 py-6">
            {[...Array(8)].map((_, i) => (
              <div key={i}>
                <Skeleton className="h-5 w-1/3 mb-2" />
                <div className="flex items-center gap-2">
                  <Skeleton className="h-10 flex-grow" />
                  <Skeleton className="w-8 h-8 rounded-md" />
                </div>
              </div>
            ))}
          </CardContent>
          <CardFooter>
            <Skeleton className="h-10 w-32" />
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
            <Palette className="h-8 w-8 text-primary" />
            <h1 className="text-3xl font-bold text-primary">Website Theme Settings</h1>
        </div>
        <Dialog open={isCreateThemeDialogOpen} onOpenChange={setIsCreateThemeDialogOpen}>
            <DialogTrigger asChild>
                <Button onClick={() => { createThemeForm.reset({ name: "", settings: { ...defaultNewThemeSettings }}); setIsCreateThemeDialogOpen(true);}}>
                    <PlusCircle className="mr-2 h-4 w-4" /> Create New Theme
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>Create New Theme</DialogTitle>
                    <DialogDescription>Define the name and HSL color values for your new theme.</DialogDescription>
                </DialogHeader>
                <Form {...createThemeForm}>
                    <form onSubmit={createThemeForm.handleSubmit(onSubmitCreateNewTheme)} className="space-y-4 py-4">
                        <FormField
                            control={createThemeForm.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Theme Name</FormLabel>
                                    <FormControl><Input placeholder="e.g., Midnight Blue" {...field} /></FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <Card className="pt-4">
                            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                                {(Object.keys(defaultNewThemeSettings) as Array<keyof ThemeSettings>).map((settingKey) => (
                                    <FormField
                                        key={`create-${settingKey}`}
                                        control={createThemeForm.control}
                                        name={`settings.${settingKey}`}
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="capitalize">{settingKey.replace(/([A-Z])/g, ' $1')}</FormLabel>
                                                <div className="flex items-center gap-2">
                                                <FormControl>
                                                    <Input {...field} disabled={createThemeForm.formState.isSubmitting} />
                                                </FormControl>
                                                <div className="w-8 h-8 rounded-md border" style={{ backgroundColor: `hsl(${field.value || '0 0% 0%'})` }}></div>
                                                </div>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                ))}
                            </CardContent>
                        </Card>
                        <DialogFooter>
                           <Button type="button" variant="outline" onClick={() => setIsCreateThemeDialogOpen(false)} disabled={createThemeForm.formState.isSubmitting}>Cancel</Button>
                            <Button type="submit" disabled={createThemeForm.formState.isSubmitting}>
                                {createThemeForm.formState.isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                                Save New Theme
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
      </div>

      <Card className="shadow-md">
        <CardHeader>
          <CardTitle>Select a Theme</CardTitle>
          <CardDescription>
            Choose a color theme for the entire website. Changes will be applied immediately and saved globally.
            Themes are sourced from themes.json.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {availableThemes.length === 0 ? (
            <p className="text-center text-muted-foreground">No themes available or failed to load themes.</p>
          ) : (
            <RadioGroup
              value={currentTheme.id}
              onValueChange={(themeId) => setTheme(themeId)}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {availableThemes.map((themeOption) => (
                <ShadcnLabel 
                  key={themeOption.id}
                  htmlFor={themeOption.id}
                  className={`flex flex-col items-start rounded-lg border p-4 cursor-pointer transition-all hover:shadow-lg
                    ${currentTheme.id === themeOption.id ? "border-primary ring-2 ring-primary shadow-xl" : "border-border hover:border-primary/50"}`}
                >
                  <div className="flex justify-between w-full items-center mb-3">
                    <span className="font-semibold text-xl text-primary">{themeOption.name}</span>
                    <div className="flex items-center">
                      {themeOption.id !== 'default' && themeOption.id !== currentTheme.id && (
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-7 w-7 mr-2 text-destructive hover:bg-destructive/10"
                          onClick={(e) => { e.preventDefault(); e.stopPropagation(); setThemeToDelete(themeOption); }}
                          aria-label={`Delete theme ${themeOption.name}`}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                      <RadioGroupItem value={themeOption.id} id={themeOption.id} className="h-5 w-5" />
                    </div>
                  </div>

                  <div className="flex space-x-2 mb-3">
                    <div className="w-8 h-8 rounded-full border-2 border-card shadow" style={{ backgroundColor: `hsl(${themeOption.settings.background})` }} title={`Background: ${themeOption.settings.background}`}></div>
                    <div className="w-8 h-8 rounded-full border-2 border-card shadow" style={{ backgroundColor: `hsl(${themeOption.settings.foreground})` }} title={`Foreground: ${themeOption.settings.foreground}`}></div>
                    <div className="w-8 h-8 rounded-full border-2 border-card shadow" style={{ backgroundColor: `hsl(${themeOption.settings.primary})` }} title={`Primary: ${themeOption.settings.primary}`}></div>
                    <div className="w-8 h-8 rounded-full border-2 border-card shadow" style={{ backgroundColor: `hsl(${themeOption.settings.secondary})` }} title={`Secondary: ${themeOption.settings.secondary}`}></div>
                    <div className="w-8 h-8 rounded-full border-2 border-card shadow" style={{ backgroundColor: `hsl(${themeOption.settings.accent})` }} title={`Accent: ${themeOption.settings.accent}`}></div>
                  </div>

                  {currentTheme.id === themeOption.id && (
                    <div className="flex items-center text-sm text-green-600 pt-2 font-medium">
                      <CheckCircle className="w-4 h-4 mr-1" />
                      Currently Active
                    </div>
                  )}
                </ShadcnLabel>
              ))}
            </RadioGroup>
          )}
        </CardContent>
      </Card>

      {currentTheme && currentTheme.id !== initialDefaultTheme.id && ( 
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>Edit Colors for: <span className="text-accent">{currentTheme.name}</span></CardTitle>
            <CardDescription>
              Modify the HSL color values. Changes are previewed live. Click "Save Color Changes" to persist.
              (Format: hue saturation% lightness% e.g., '210 100% 50%')
            </CardDescription>
          </CardHeader>
          <Form {...editThemeForm}>
            <form onSubmit={editThemeForm.handleSubmit(onSubmitEditThemeSettings)}>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                {(Object.keys(currentTheme.settings) as Array<keyof ThemeSettings>).map((settingKey) => (
                  <FormField
                    key={settingKey}
                    control={editThemeForm.control}
                    name={settingKey}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="capitalize">{settingKey.replace(/([A-Z])/g, ' $1')}</FormLabel>
                        <div className="flex items-center gap-2">
                          <FormControl>
                            <Input
                              {...field}
                              onChange={(e) => {
                                field.onChange(e);
                                handleApplyPreview(settingKey, e.target.value, 'edit');
                              }}
                              disabled={editThemeForm.formState.isSubmitting}
                            />
                          </FormControl>
                          <div className="w-8 h-8 rounded-md border" style={{ backgroundColor: `hsl(${field.value})` }}></div>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ))}
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={editThemeForm.formState.isSubmitting}>
                  {editThemeForm.formState.isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                  Save Color Changes
                </Button>
              </CardFooter>
            </form>
          </Form>
        </Card>
      )}

      <AlertDialog open={!!themeToDelete} onOpenChange={(isOpen) => { if(!isOpen) setThemeToDelete(null);}}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the theme
              "<span className="font-semibold">{themeToDelete?.name}</span>".
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setThemeToDelete(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className={buttonVariants({ variant: "destructive" })}
            >
              Delete Theme
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

    </div>
  );
}

