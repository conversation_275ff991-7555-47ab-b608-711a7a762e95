
"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button, buttonVariants } from "@/components/ui/button";
import type { VPSPlan } from "@/types";
import { getAllVpsPlans, createVpsPlan, updateVpsPlan, deleteVpsPlan, type PlanActionResult } from "@/lib/actions/vpsPlanActions";
import { MoreVertical, ListPlus, ServerIcon, Save, Loader2, Edit, Trash2, Cpu, HardDrive, MemoryStick, Zap, Tag } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { cn } from '@/lib/utils';

const specialTagOptions: (VPSPlan['specialTag'] | "" | undefined)[] = ["", "Most Bought", "Best Price", "New", "Recommended", "Sale"];

const planFormSchema = z.object({
  name: z.string().min(3, { message: "Plan name must be at least 3 characters." }).max(50, { message: "Plan name cannot exceed 50 characters." }),
  description: z.string().min(10, { message: "Description must be at least 10 characters." }).max(500, { message: "Description cannot exceed 500 characters." }),
  pricePerMonth: z.coerce.number().positive({ message: "Price must be a positive number." }),
  originalPricePerMonth: z.union([z.coerce.number().positive(), z.literal('')]).optional().transform(val => val === '' ? undefined : val), 
  discountLabel: z.string().max(20, { message: "Discount label cannot exceed 20 characters." }).optional().transform(val => val === '' ? undefined : val),
  specialTag: z.enum(specialTagOptions as [string, ...string[]]).optional().transform(val => val === '' ? undefined : val),
  cpuCores: z.coerce.number().int({ message: "CPU Cores must be an integer." }).positive({ message: "CPU Cores must be a positive integer." }),
  ramGB: z.coerce.number().int({ message: "RAM must be an integer." }).positive({ message: "RAM must be a positive integer." }),
  storageGB: z.coerce.number().int({ message: "Storage must be an integer." }).positive({ message: "Storage must be a positive integer." }),
  bandwidthTB: z.coerce.number().positive({ message: "Bandwidth must be a positive number." }),
  features: z.string().min(1, { message: "Please list at least one feature (each on a new line)." }),
});

type PlanFormData = z.infer<typeof planFormSchema>;

const INTERNAL_NONE_VALUE = "_INTERNAL_NONE_VALUE_";

const getSpecialTagBgColor = (tag?: VPSPlan['specialTag']) => {
  switch (tag) {
    case 'Sale':
      return 'bg-destructive text-destructive-foreground';
    case 'Most Bought':
      return 'bg-primary text-primary-foreground';
    case 'Best Price':
      return 'bg-green-600 text-white'; 
    case 'New':
      return 'bg-blue-500 text-white'; 
    case 'Recommended':
      return 'bg-accent text-accent-foreground';
    default:
      return 'bg-primary text-primary-foreground';
  }
};

const getSpecialTagBorderClass = (tag?: VPSPlan['specialTag']) => {
  if (!tag) return ''; 

  switch (tag) {
    case 'Sale':
      return 'border-2 border-destructive';
    case 'Most Bought':
      return 'border-2 border-primary';
    case 'Best Price':
      return 'border-2 border-green-600';
    case 'New':
      return 'border-2 border-blue-500';
    case 'Recommended':
      return 'border-2 border-accent';
    default:
      return ''; 
  }
};

export default function AdminVpsPlansPage() {
  const [plans, setPlans] = useState<VPSPlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isPlanDialogOpen, setIsPlanDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<'create' | 'edit'>('create');
  const [currentPlanToEdit, setCurrentPlanToEdit] = useState<VPSPlan | null>(null);
  const [planToDelete, setPlanToDelete] = useState<VPSPlan | null>(null);
  const { toast } = useToast();

  const form = useForm<PlanFormData>({
    resolver: zodResolver(planFormSchema),
    defaultValues: {
      name: "",
      description: "",
      pricePerMonth: 0,
      originalPricePerMonth: "",
      discountLabel: "",
      specialTag: "",
      cpuCores: 1,
      ramGB: 1,
      storageGB: 20,
      bandwidthTB: 1,
      features: "",
    },
  });

  const fetchPlans = async () => {
    setIsLoading(true);
    try {
      const fetchedPlans = await getAllVpsPlans();
      setPlans(fetchedPlans);
    } catch (error) {
      toast({
        title: "Error fetching plans",
        description: error instanceof Error ? error.message : "Could not load plan data.",
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPlans();
  }, []);

  const handleOpenCreateDialog = () => {
    setDialogMode('create');
    setCurrentPlanToEdit(null);
    form.reset({
      name: "", description: "", pricePerMonth: 0, originalPricePerMonth: "",
      discountLabel: "", specialTag: "", cpuCores: 1, ramGB: 1,
      storageGB: 20, bandwidthTB: 1, features: ""
    });
    setIsPlanDialogOpen(true);
  };

  const handleOpenEditDialog = (plan: VPSPlan) => {
    setDialogMode('edit');
    setCurrentPlanToEdit(plan);
    form.reset({
      ...plan,
      originalPricePerMonth: plan.originalPricePerMonth ?? "",
      features: plan.features.join('\\n'), 
      specialTag: plan.specialTag ?? "", 
    });
    setIsPlanDialogOpen(true);
  };

  const handlePlanFormSubmit = async (data: PlanFormData) => {
    let result: PlanActionResult;
    form.clearErrors(); // Clear previous server errors

    if (dialogMode === 'create') {
      result = await createVpsPlan(data);
    } else if (currentPlanToEdit) {
      result = await updateVpsPlan(currentPlanToEdit.id, data);
    } else {
      return; // Should not happen
    }

    if (result.success && result.plan) {
      toast({
        title: `Plan ${dialogMode === 'create' ? 'Created' : 'Updated'} Successfully`,
        description: `Plan "${result.plan.name}" has been ${dialogMode === 'create' ? 'added' : 'updated'}.`,
        variant: "success",
      });
      setIsPlanDialogOpen(false);
      fetchPlans();
    } else {
      if (result.fieldErrors) {
        Object.entries(result.fieldErrors).forEach(([field, messages]) => {
          if (messages && messages.length > 0) {
            form.setError(field as keyof PlanFormData, { type: 'server', message: messages[0] });
          }
        });
      }
      toast({
        title: `Failed to ${dialogMode} Plan`,
        description: result.error || "An unknown error occurred. Please check the input fields.",
        variant: "error",
      });
    }
  };

  const handleDeletePlanConfirm = async () => {
    if (!planToDelete) return;
    const result = await deleteVpsPlan(planToDelete.id);
    if (result.success) {
      toast({
        title: "Plan Deleted",
        description: `Plan "${planToDelete.name}" has been removed.`,
        variant: "info" 
      });
      fetchPlans();
    } else {
      toast({
        title: "Deletion Failed",
        description: result.error || "Could not delete plan.",
        variant: "error",
      });
    }
    setPlanToDelete(null);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center flex-1 p-6 md:p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="ml-2 text-lg text-muted-foreground">Loading VPS plans...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <ServerIcon className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold text-primary">VPS Plan Management</h1>
        </div>
        <Button onClick={handleOpenCreateDialog}>
          <ListPlus className="mr-2 h-5 w-5" /> Create New Plan
        </Button>
      </div>
      
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle>All VPS Plans</CardTitle>
          <CardDescription>
            View, manage, and configure VPS hosting plans offered on SkyHosting. Plan data is sourced from vps-plans.json.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {plans.length === 0 ? (
            <div className="text-center py-12">
              <ServerIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold text-primary mb-2">No VPS Plans Found</h3>
              <p className="text-muted-foreground">
                There are currently no VPS plans configured in the system. Click "Create New Plan" to add one.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {plans.map((plan) => (
                <Card key={plan.id} className={cn(
                    "flex flex-col h-full shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-lg",
                    getSpecialTagBorderClass(plan.specialTag)
                  )}>
                   {plan.specialTag && (
                    <div
                      className={cn(
                        "absolute top-0 -mt-3 left-4 text-sm font-semibold px-2 py-1 rounded-md shadow-md z-10",
                        getSpecialTagBgColor(plan.specialTag)
                      )}
                    >
                      {plan.specialTag}
                    </div>
                  )}
                  {plan.discountLabel && plan.originalPricePerMonth && plan.pricePerMonth < plan.originalPricePerMonth && (
                    <div className="absolute top-0 -mt-3 right-4 z-10 shadow-md">
                      <Badge variant="destructive" className="text-sm px-2 py-1 rounded-md hover:bg-red-500 hover:text-white hover:opacity-100 hover:shadow-none">
                        {plan.discountLabel}
                      </Badge>
                    </div>
                  )}
                  <CardHeader className="pb-2 pt-8"> 
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-xl font-semibold text-primary mb-1">{plan.name}</CardTitle>
                        <CardDescription className="text-xs text-muted-foreground">ID: {plan.id}</CardDescription>
                      </div>
                       <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleOpenEditDialog(plan)}>
                            <Edit className="mr-2 h-4 w-4" /> Edit Plan
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            className="text-destructive focus:text-destructive-foreground focus:bg-destructive"
                            onSelect={(e) => { e.preventDefault(); setPlanToDelete(plan); }}
                          >
                            <Trash2 className="mr-2 h-4 w-4" /> Delete Plan
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3 text-sm flex-grow pt-2">
                    <div className="flex justify-between items-baseline mb-3"> 
                      <span className="text-muted-foreground">Price:</span>
                      {plan.originalPricePerMonth && plan.pricePerMonth < plan.originalPricePerMonth ? (
                        <div className="flex flex-row items-baseline gap-x-2"> 
                          <span className="text-base font-normal line-through text-muted-foreground"> 
                            ₹{plan.originalPricePerMonth}
                          </span>
                          <span className="text-2xl font-bold text-accent"> 
                            ₹{plan.pricePerMonth}
                            <span className="text-sm font-normal text-muted-foreground">/mo</span> 
                          </span>
                        </div>
                      ) : (
                        <span className="text-2xl font-bold text-accent"> 
                          ₹{plan.pricePerMonth}
                          <span className="text-sm font-normal text-muted-foreground">/mo</span> 
                        </span>
                      )}
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-1 text-xs">
                        <div className="flex items-center"><Cpu className="w-3 h-3 mr-1 text-muted-foreground"/>{plan.cpuCores} Core(s)</div>
                        <div className="flex items-center"><MemoryStick className="w-3 h-3 mr-1 text-muted-foreground"/>{plan.ramGB}GB RAM</div>
                        <div className="flex items-center"><HardDrive className="w-3 h-3 mr-1 text-muted-foreground"/>{plan.storageGB}GB SSD</div>
                        <div className="flex items-center"><Zap className="w-3 h-3 mr-1 text-muted-foreground"/>{plan.bandwidthTB}TB B/W</div>
                    </div>
                     <div>
                        <p className="text-xs text-muted-foreground mt-1 font-medium">Features:</p>
                        <ul className="list-disc list-inside text-xs text-muted-foreground pl-2">
                            {plan.features.slice(0,3).map((feat, i) => <li key={i} className="truncate" title={feat}>{feat}</li>)}
                            {plan.features.length > 3 && <li className="text-accent text-xs hover:underline cursor-pointer" onClick={() => handleOpenEditDialog(plan)}>...and {plan.features.length -3} more</li>}
                        </ul>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={isPlanDialogOpen} onOpenChange={setIsPlanDialogOpen}>
          <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{dialogMode === 'create' ? 'Create New' : 'Edit'} VPS Plan</DialogTitle>
              <DialogDescription>
                Fill in the details for the VPS plan. Click save when you're done.
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handlePlanFormSubmit)} className="space-y-4 py-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Plan Name</FormLabel>
                      <FormControl><Input placeholder="e.g., Basic Cloud" {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl><Textarea placeholder="Brief description of the plan..." {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="pricePerMonth"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Price (₹/mo)</FormLabel>
                        <FormControl><Input type="number" step="0.01" placeholder="e.g., 500" {...field} /></FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="originalPricePerMonth"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Original Price (₹/mo) (Optional)</FormLabel>
                        <FormControl><Input type="number" step="0.01" placeholder="e.g., 700" {...field} /></FormControl>
                         <FormDescription>Leave blank if no discount.</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="discountLabel"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Discount Label (Optional)</FormLabel>
                        <FormControl><Input placeholder="e.g., Save 20%" {...field} /></FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="specialTag"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Special Tag (Optional)</FormLabel>
                        <Select 
                          onValueChange={(valueFromSelect) => {
                            field.onChange(valueFromSelect === INTERNAL_NONE_VALUE ? "" : valueFromSelect);
                          }} 
                          value={field.value === "" || field.value === undefined ? INTERNAL_NONE_VALUE : field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a tag" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value={INTERNAL_NONE_VALUE}> 
                              None
                            </SelectItem>
                            {specialTagOptions.filter(tag => typeof tag === 'string' && tag !== "").map(tag => (
                              <SelectItem key={String(tag)} value={String(tag)}> 
                                {tag}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                 <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <FormField control={form.control} name="cpuCores" render={({ field }) => (
                        <FormItem><FormLabel>CPU Cores</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
                    )}/>
                    <FormField control={form.control} name="ramGB" render={({ field }) => (
                        <FormItem><FormLabel>RAM (GB)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
                    )}/>
                    <FormField control={form.control} name="storageGB" render={({ field }) => (
                        <FormItem><FormLabel>Storage (GB)</FormLabel><FormControl><Input type="number" {...field} /></FormControl><FormMessage /></FormItem>
                    )}/>
                    <FormField control={form.control} name="bandwidthTB" render={({ field }) => (
                        <FormItem><FormLabel>Bandwidth (TB)</FormLabel><FormControl><Input type="number" step="0.1" {...field} /></FormControl><FormMessage /></FormItem>
                    )}/>
                 </div>
                <FormField
                  control={form.control}
                  name="features"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Features</FormLabel>
                      <FormControl><Textarea placeholder="List features, one per line..." {...field} rows={4} /></FormControl>
                      <FormDescription>Enter each feature on a new line. Use '\\n' for line breaks if editing raw JSON.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setIsPlanDialogOpen(false)} disabled={form.formState.isSubmitting}>Cancel</Button>
                  <Button type="submit" disabled={form.formState.isSubmitting}>
                    {form.formState.isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                    {dialogMode === 'create' ? 'Save Plan' : 'Update Plan'}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>

        <AlertDialog open={!!planToDelete} onOpenChange={(isOpen) => { if(!isOpen) setPlanToDelete(null);}}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the VPS plan
                "<span className="font-semibold">{planToDelete?.name}</span>". 
                Consider if this plan is used by any active orders before deleting.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setPlanToDelete(null)}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeletePlanConfirm}
                className={buttonVariants({ variant: "destructive" })}
              >
                Delete Plan
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
    </div>
  );
}
