// @ts-nocheck
'use server';

import bcryptjs from 'bcryptjs';
import prisma from './prisma';
import { Prisma } from '@prisma/client';
import * as z from "zod";
import type { UserRole, UserStatus, ActivityType } from '@prisma/client';
import { headers } from 'next/headers'; // For accessing request headers

// Schema for basic input format validation
const loginFormSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address." }),
  password: z.string().min(1, { message: "Password cannot be empty." }),
});

const signupFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }).max(50, { message: "Name cannot exceed 50 characters."}),
  email: z.string().email({ message: "Invalid email address." }),
  password: z.string().min(8, { message: "Password must be at least 8 characters." }),
});

const userProfileUpdateSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters.").max(50, "Name cannot exceed 50 characters.").optional(),
  email: z.string().email("Invalid email address.").optional(),
  is2FAEnabled: z.boolean().optional(),
});


export interface UserWithStoredPassword {
  id: string;
  email: string;
  password?: string; // This stores the HASH
  name?: string | null;
  is2FAEnabled?: boolean;
  role?: UserRole;
  status?: UserStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthActionResult {
  sessionId?: string;
  user?: Omit<UserWithStoredPassword, 'password'>;
  error?: string;
  fieldErrors?: Partial<Record<keyof z.infer<typeof signupFormSchema> | keyof z.infer<typeof loginFormSchema>, string[]>>;
  success: boolean; // Standardized to always include success
}

interface UpdateUserPersistenceResult {
  success: boolean;
  user?: Omit<UserWithStoredPassword, 'password'>;
  error?: string;
  fieldErrors?: Partial<Record<keyof z.infer<typeof userProfileUpdateSchema>, string[]>>;
}


export async function loginUser(email: string, passwordInput: string): Promise<AuthActionResult> {
  const validationResult = loginFormSchema.safeParse({ email, password: passwordInput });
  if (!validationResult.success) {
    return {
      success: false,
      error: "Validation failed. Please check the fields below.",
      fieldErrors: validationResult.error.flatten().fieldErrors as AuthActionResult['fieldErrors']
    };
  }

  try {
    const userRecord = await prisma.user.findUnique({ where: { email } });

    if (!userRecord || !userRecord.password) {
      return { success: false, error: 'Invalid email or password.', fieldErrors: { email: ["Invalid email or password."], password: ["Invalid email or password."] } };
    }

    const passwordMatches = bcryptjs.compareSync(passwordInput, userRecord.password);
    if (!passwordMatches) {
      return { success: false, error: 'Invalid email or password.', fieldErrors: { email: ["Invalid email or password."], password: ["Invalid email or password."] } };
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...userToReturn } = userRecord;

    await prisma.session.deleteMany({ where: { userId: userToReturn.id } });

    const requestHeaders = headers();
    const userAgent = requestHeaders.get('user-agent') || undefined;
    const ipAddress = requestHeaders.get('x-forwarded-for')?.split(',')[0].trim() || requestHeaders.get('x-real-ip') || undefined;

    const sessionId = `sess_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour expiry

    await prisma.session.create({
      data: {
        sessionId,
        userId: userToReturn.id,
        expiresAt,
        userAgent,
        ipAddress,
      }
    });

    await prisma.activityLogEntry.create({
      data: {
        userId: userToReturn.id,
        timestamp: new Date(),
        type: 'LOGIN_SUCCESS' as ActivityType,
        description: `Successfully logged in.`,
        icon: 'LogIn'
      }
    });

    return { sessionId, user: userToReturn, success: true };
  } catch (e) {
    console.error("Login error:", e);
    return { success: false, error: "An unexpected error occurred during login." };
  }
}

export async function signupUser(name: string, emailInput: string, passwordInput: string): Promise<AuthActionResult> {
  const validationResult = signupFormSchema.safeParse({ name, email: emailInput, password: passwordInput });

  if (!validationResult.success) {
    return {
      success: false,
      error: "Validation failed. Please check the fields below.",
      fieldErrors: validationResult.error.flatten().fieldErrors as AuthActionResult['fieldErrors']
    };
  }

  const validatedData = validationResult.data;

  try {
    const existingUser = await prisma.user.findUnique({ where: { email: validatedData.email } });
    if (existingUser) {
      return {
        success: false,
        error: 'User with this email already exists.',
        fieldErrors: { email: ["This email address is already in use."] }
      };
    }

    const salt = bcryptjs.genSaltSync(10);
    const hashedPassword = bcryptjs.hashSync(validatedData.password, salt);

    const newUserRecord = await prisma.user.create({
      data: {
        name: validatedData.name,
        email: validatedData.email,
        password: hashedPassword,
        role: 'USER' as UserRole,
        is2FAEnabled: false,
        status: 'ACTIVE' as UserStatus,
      }
    });

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...userToReturn } = newUserRecord;

    await prisma.session.deleteMany({ where: { userId: userToReturn.id } });

    const requestHeaders = headers();
    const userAgent = requestHeaders.get('user-agent') || undefined;
    const ipAddress = requestHeaders.get('x-forwarded-for')?.split(',')[0].trim() || requestHeaders.get('x-real-ip') || undefined;

    const sessionId = `sess_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour expiry

    await prisma.session.create({
      data: {
        sessionId,
        userId: userToReturn.id,
        expiresAt,
        userAgent,
        ipAddress,
      }
    });

    await prisma.activityLogEntry.create({
      data: {
        userId: userToReturn.id,
        timestamp: new Date(),
        type: 'LOGIN_SUCCESS' as ActivityType, // Or a new 'ACCOUNT_CREATED' type
        description: `Account created and logged in.`,
        icon: 'UserPlus'
      }
    });

    return { sessionId, user: userToReturn, success: true };
  } catch (e) {
    console.error("Signup error:", e);
    return { success: false, error: "An unexpected error occurred during signup." };
  }
}


export async function updateUserPersistence(userId: string, updates: Partial<Omit<UserWithStoredPassword, 'id' | 'createdAt' | 'updatedAt' | 'password' >>): Promise<UpdateUserPersistenceResult> {
  if (!userId) {
    return { success: false, error: "User ID is required." };
  }

  const validationResult = userProfileUpdateSchema.safeParse(updates);
  if (!validationResult.success) {
    return {
      success: false,
      error: "Validation failed. Please check the fields below.",
      fieldErrors: validationResult.error.flatten().fieldErrors as UpdateUserPersistenceResult['fieldErrors']
    };
  }

  const validatedUpdates = validationResult.data;

  try {
    const oldUserData = await prisma.user.findUnique({ where: { id: userId } });
    if (!oldUserData) {
      return { success: false, error: 'User not found.' };
    }

    if (validatedUpdates.email && validatedUpdates.email !== oldUserData.email) {
      const existingUserWithEmail = await prisma.user.findFirst({ where: { email: validatedUpdates.email, NOT: { id: userId } } });
      if (existingUserWithEmail) {
        return {
          success: false,
          error: 'This email address is already in use by another account.',
          fieldErrors: { email: ['This email address is already in use by another account.'] }
        };
      }
    }

    const updateData: { name?: string; email?: string; is2FAEnabled?: boolean } = {};
    if (validatedUpdates.name !== undefined) updateData.name = validatedUpdates.name;
    if (validatedUpdates.email !== undefined) updateData.email = validatedUpdates.email;
    if (validatedUpdates.is2FAEnabled !== undefined) updateData.is2FAEnabled = validatedUpdates.is2FAEnabled;


    const updatedUserRecord = await prisma.user.update({
      where: { id: userId },
      data: updateData,
    });

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...userWithoutPassword } = updatedUserRecord;

    let updateDescription = "Profile details updated.";
    if (validatedUpdates.is2FAEnabled !== undefined && validatedUpdates.is2FAEnabled !== oldUserData.is2FAEnabled) {
      updateDescription = `Two-Factor Authentication ${validatedUpdates.is2FAEnabled ? 'enabled' : 'disabled'}.`;
    } else if (validatedUpdates.name && validatedUpdates.name !== oldUserData.name) {
      updateDescription = "Name updated.";
    } else if (validatedUpdates.email && validatedUpdates.email !== oldUserData.email) {
      updateDescription = "Email address updated.";
    }

    await prisma.activityLogEntry.create({
      data: {
        userId,
        timestamp: new Date(),
        type: 'PROFILE_UPDATED' as ActivityType,
        description: updateDescription,
        icon: 'UserCog'
      }
    });

    return { success: true, user: userWithoutPassword };
  } catch (e) {
    console.error("Error updating user persistence:", e);
    return { success: false, error: "Failed to update user details." };
  }
}


export async function validateSession(sessionId: string | null): Promise<{ user: Omit<UserWithStoredPassword, 'password'> | null; error?: string }> {
  if (!sessionId) {
    return { user: null, error: "No session ID provided." };
  }
  try {
    const session = await prisma.session.findUnique({
      where: { sessionId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            is2FAEnabled: true,
            status: true,
            createdAt: true,
            updatedAt: true,
          }
        }
      }
    });

    if (!session) {
      return { user: null, error: "Session not found." };
    }

    if (new Date() >= new Date(session.expiresAt)) {
      await prisma.session.delete({ where: { sessionId } });
      return { user: null, error: "Session expired." };
    }

    if (!session.user) {
        await prisma.session.delete({ where: { sessionId }});
        return { user: null, error: "User for session not found." };
    }
    
    // Placeholder for User-Agent / IP Address check
    // const requestHeaders = headers();
    // const currentUserAgent = requestHeaders.get('user-agent');
    // const currentIpAddress = requestHeaders.get('x-forwarded-for')?.split(',')[0].trim() || requestHeaders.get('x-real-ip');
    // if (session.userAgent && session.userAgent !== currentUserAgent) { /* Handle mismatch */ }
    // if (session.ipAddress && session.ipAddress !== currentIpAddress) { /* Handle mismatch */ }

    return { user: session.user as Omit<UserWithStoredPassword, 'password'> };

  } catch (error) {
    console.error("Error validating session:", error);
    return { user: null, error: "Failed to validate session." };
  }
}

export async function logoutUser(sessionId: string | null): Promise<{ success: boolean; error?: string }> {
  if (!sessionId) {
    return { success: false, error: "No session ID provided for logout." };
  }
  try {
    const deletedSession = await prisma.session.deleteMany({ where: { sessionId } });
    if (deletedSession.count > 0) {
      return { success: true };
    } else {
      // Session might have already expired and been cleaned up, or was invalid.
      // This is not strictly an error from logout perspective.
      console.warn(`Logout attempt for non-existent or already removed session ID: ${sessionId}`);
      return { success: true }; 
    }
  } catch (error) {
    console.error("Error logging out user (server-side):", error);
    return { success: false, error: "Failed to invalidate session on server." };
  }
}
