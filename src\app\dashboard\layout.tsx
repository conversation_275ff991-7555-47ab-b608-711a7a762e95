
"use client"; 

import type { ReactNode } from 'react';
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Loader2 } from 'lucide-react'; // For loading state

export default function DashboardLayout({ children }: { children: ReactNode }) {
  const { isAuthenticated, user, isLoadingAuth } = useAuth(); // Added isLoadingAuth
  const router = useRouter();

  useEffect(() => {
    if (!isLoadingAuth) { // Only check after auth state is determined
      if (isAuthenticated === false) { 
        router.push('/login');
      }
    }
  }, [isAuthenticated, router, isLoadingAuth]);

  if (isLoadingAuth) { // Show loading state while auth is being checked
    return (
      <div className="flex flex-col items-center justify-center flex-1 p-6 md:p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-2 text-lg text-muted-foreground">Loading dashboard...</p>
      </div>
    );
  }
  
  if (!isAuthenticated) { // If still not authenticated after loading
    return (
      <div className="flex flex-col items-center justify-center flex-1 p-6 md:p-8">
        <p className="text-lg text-muted-foreground mb-4">Redirecting to login...</p>
        <Button asChild><Link href="/login">Go to Login</Link></Button>
      </div>
    );
  }
  
  return (
    <div className="flex-1 p-6 md:p-8 bg-muted/20 rounded-lg shadow-inner">
      {children}
    </div>
  );
}
