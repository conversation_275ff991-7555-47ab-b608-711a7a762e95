
"use client";
import Link from 'next/link';
import { Logo } from '@/components/shared/Logo';
import { UserNav } from '@/components/layout/UserNav';
import { Button } from '@/components/ui/button';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Menu, X, LayoutDashboard, ShieldAlert, Users, Palette, ShoppingBag, CreditCard, LifeBuoy, ChevronDown, Ticket as TicketIcon, Server, ListPlus, Settings, LogOut, LogIn, UserPlus as UserPlusIcon } from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const baseNavLinks = [
  { href: '/', label: 'Home' },
  { href: '/vps-offerings', label: 'VPS Offerings' },
];

const dashboardNavLinks = [
  { href: "/dashboard", label: "Overview", icon: LayoutDashboard },
  { href: "/dashboard/orders", label: "My Services", icon: Server },
  { href: "/dashboard/payment-history", label: "Payment History", icon: CreditCard },
  { href: "/support", label: "Support Tickets", icon: LifeBuoy },
];

const adminNavLinks = [
  { href: "/admin", label: "Admin Overview", icon: LayoutDashboard },
  { href: "/admin/users", label: "User Management", icon: Users },
  { href: "/admin/vps-management", label: "VPS Management", icon: Server },
  { href: "/admin/vps-plans", label: "VPS Plans", icon: ListPlus },
  { href: "/admin/payments", label: "Payments", icon: CreditCard },
  { href: "/admin/fraud-detection", label: "Fraud Detection", icon: ShieldAlert },
  { href: "/admin/tickets", label: "Support Tickets", icon: TicketIcon },
  { href: "/admin/theme-settings", label: "Theme Settings", icon: Palette },
];


export function Navbar() {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const { isAuthenticated, isAdmin, logout } = useAuth(); 

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Close mobile menu on route change
  useEffect(() => {
    if (isMobileMenuOpen) {
      setIsMobileMenuOpen(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // Effect to handle body scroll when mobile menu opens/closes
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    // Cleanup function to reset body overflow when component unmounts or menu closes
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);


  if (!isMounted) {
    return (
      <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 h-16">
        <div className="container mx-auto flex h-full max-w-screen-2xl items-center justify-between px-4 sm:px-6 lg:px-8">
          <Logo />
          <div className="hidden md:flex items-center space-x-4">
             {/* Minimal placeholders for other elements */}
          </div>
          <div className="md:hidden">
             <div className="h-10 w-10 flex items-center justify-center">
                <Menu className="h-6 w-6" />
             </div>
          </div>
        </div>
      </header>
    );
  }


  return (
    <>
      <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto flex h-16 max-w-screen-2xl items-center justify-between px-4 sm:px-6 lg:px-8">
          <Logo />
          <nav className="hidden md:flex items-center space-x-1 text-sm font-medium">
            {baseNavLinks.map((link) => (
              <Button variant="ghost" asChild key={link.href}>
                  <Link
                  href={link.href}
                  className={cn(
                      "transition-colors hover:text-accent",
                      pathname === link.href ? "text-accent font-semibold" : "text-foreground/80"
                  )}
                  >
                  {link.label}
                  </Link>
              </Button>
            ))}

            {isAuthenticated && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost">
                    Dashboard <ChevronDown className="ml-1 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  {dashboardNavLinks.map((item) => (
                    <DropdownMenuItem key={item.href} asChild>
                      <Link href={item.href} className={cn(pathname.startsWith(item.href) && item.href !== "/dashboard" && !item.href.includes("support") ? "bg-accent text-accent-foreground" : pathname === item.href ? "bg-accent text-accent-foreground" : "")}>
                        <item.icon className="mr-2 h-4 w-4" />
                        <span>{item.label}</span>
                      </Link>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {isAuthenticated && isAdmin && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost">
                    Admin Panel <ChevronDown className="ml-1 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  {adminNavLinks.map((item) => (
                    <DropdownMenuItem key={item.href} asChild>
                      <Link href={item.href} className={cn(pathname.startsWith(item.href) && item.href !== "/admin" ? "bg-accent text-accent-foreground" : pathname === item.href ? "bg-accent text-accent-foreground" : "")}>
                        <item.icon className="mr-2 h-4 w-4" />
                        <span>{item.label}</span>
                      </Link>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </nav>
          <div className="hidden md:flex items-center space-x-4">
            <UserNav />
          </div>
          <div className="md:hidden">
            <Button variant="ghost" size="icon" onClick={toggleMobileMenu} aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}>
              {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>
      </header>
      
      <div 
        className={cn(
          "md:hidden fixed inset-x-0 top-16 bottom-0 z-40 bg-background/95 backdrop-blur-sm supports-[backdrop-filter]:bg-background/80 overflow-y-auto transform transition-transform duration-300 ease-in-out",
          isMobileMenuOpen ? 'translate-y-0' : '-translate-y-full' // Use -translate-y-full to hide it above screen
        )}
      >
        {isMobileMenuOpen && ( // Conditionally render content to ensure focus management and accessibility
          <nav className="flex flex-col items-start space-y-1 px-4 py-6">
            {baseNavLinks.map((link) => (
              <Button variant="ghost" className="w-full justify-start text-base py-3" asChild key={link.href}>
                <Link
                    href={link.href}
                    className={cn(pathname === link.href ? "text-accent font-semibold" : "text-foreground/80")}
                    onClick={() => setIsMobileMenuOpen(false)}
                >
                    {link.label}
                </Link>
              </Button>
            ))}
            {isAuthenticated && (
                <>
                    <DropdownMenuSeparator className="my-2 w-full bg-border/40" />
                    <p className="text-sm font-medium text-muted-foreground px-2 pt-2 self-start">Dashboard</p>
                    {dashboardNavLinks.map((item) => (
                        <Button variant="ghost" className="w-full justify-start text-base py-3" asChild key={item.href}>
                            <Link href={item.href} onClick={() => setIsMobileMenuOpen(false)} className={cn(pathname.startsWith(item.href) && item.href !== "/dashboard" && !item.href.includes("support") ? "text-accent font-semibold" : pathname === item.href ? "text-accent font-semibold" : "text-foreground/80")}>
                                <item.icon className="mr-2 h-4 w-4" /> {item.label}
                            </Link>
                        </Button>
                    ))}
                </>
            )}
             {isAuthenticated && isAdmin && (
                <>
                    <DropdownMenuSeparator className="my-2 w-full bg-border/40" />
                     <p className="text-sm font-medium text-muted-foreground px-2 pt-2 self-start">Admin Panel</p>
                    {adminNavLinks.map((item) => (
                         <Button variant="ghost" className="w-full justify-start text-base py-3" asChild key={item.href}>
                            <Link href={item.href} onClick={() => setIsMobileMenuOpen(false)} className={cn(pathname.startsWith(item.href) && item.href !== "/admin" ? "text-accent font-semibold" : pathname === item.href ? "text-accent font-semibold" : "text-foreground/80")}>
                                <item.icon className="mr-2 h-4 w-4" /> {item.label}
                            </Link>
                        </Button>
                    ))}
                </>
            )}
            <DropdownMenuSeparator className="my-2 w-full bg-border/40" />
            {isAuthenticated ? (
              <>
                <Button variant="ghost" className="w-full justify-start text-base py-3" asChild>
                  <Link href="/dashboard/settings" onClick={() => setIsMobileMenuOpen(false)} className="text-foreground/80">
                    <Settings className="mr-2 h-4 w-4" /> Settings
                  </Link>
                </Button>
                <Button variant="ghost" className="w-full justify-start text-base py-3 text-foreground/80" onClick={() => { logout(); setIsMobileMenuOpen(false); }}>
                  <LogOut className="mr-2 h-4 w-4" /> Log out
                </Button>
              </>
            ) : (
              <>
                <Button variant="ghost" className="w-full justify-start text-base py-3" asChild>
                  <Link href="/login" onClick={() => setIsMobileMenuOpen(false)} className="text-foreground/80">
                    <LogIn className="mr-2 h-4 w-4" /> Login
                  </Link>
                </Button>
                <Button variant="default" className="w-full justify-center text-base py-3 mt-2" asChild>
                  <Link href="/signup" onClick={() => setIsMobileMenuOpen(false)}>
                    <UserPlusIcon className="mr-2 h-4 w-4" /> Sign Up
                  </Link>
                </Button>
              </>
            )}
          </nav>
        )}
      </div>
    </>
  );
}

    