// @ts-nocheck
'use server';

import type { AppTheme, ThemeSettings } from '@/types';
import prisma from './prisma';
import * as z from "zod";
import { Prisma } from '@prisma/client';


const hslColorStringSchema = z.string().regex(
  /^\d{1,3}\s\d{1,3}%\s\d{1,3}%$/,
  "Must be valid HSL format (e.g., '210 100% 50%')"
);

const themeSettingsSchema = z.object({
  background: hslColorStringSchema,
  foreground: hslColorStringSchema,
  primary: hslColorStringSchema,
  primaryForeground: hslColorStringSchema,
  secondary: hslColorStringSchema,
  secondaryForeground: hslColorStringSchema,
  accent: hslColorStringSchema,
  accentForeground: hslColorStringSchema,
});

const newThemeFormSchema = z.object({
  name: z.string().min(3, "Theme name must be at least 3 characters.").max(30, "Theme name cannot exceed 30 characters."),
  settings: themeSettingsSchema,
});


export async function getAvailableThemes(): Promise<AppTheme[]> {
  try {
    const themesFromDb = await prisma.appTheme.findMany();
    return themesFromDb.map(t => ({
      id: t.id,
      name: t.name,
      settings: {
        background: t.settingsBackground,
        foreground: t.settingsForeground,
        primary: t.settingsPrimary,
        primaryForeground: t.settingsPrimaryForeground,
        secondary: t.settingsSecondary,
        secondaryForeground: t.settingsSecondaryForeground,
        accent: t.settingsAccent,
        accentForeground: t.settingsAccentForeground,
      }
    }));
  } catch (error) {
    console.error('Error in getAvailableThemes:', error);
    return [
        {
          id: 'default-fallback-error',
          name: 'Default (Error)',
          settings: {
            background: "0 0% 94%", 
            foreground: "0 0% 10%", 
            primary: "210 100% 50%",
            primaryForeground: "0 0% 100%",
            secondary: "0 0% 85%", 
            secondaryForeground: "0 0% 10%", 
            accent: "0 100% 50%", 
            accentForeground: "0 0% 100%", 
          },
        },
      ];
  }
}

export async function getActiveThemeId(): Promise<string> {
  try {
    const siteSettings = await prisma.siteSettings.findUnique({ where: { id: "global_settings" } });
    if (siteSettings && siteSettings.activeThemeId) {
        return siteSettings.activeThemeId;
    }
    console.warn(`Site settings not found or activeThemeId not set. Falling back to 'default' theme ID.`);
    return 'default';

  } catch (error) {
    console.error(`Error reading site settings:`, error);
    return 'default';
  }
}

export async function setActiveThemeId(themeId: string): Promise<{ success: boolean; error?: string }> {
  if (!themeId) return { success: false, error: "Theme ID is required." };
  try {
    await prisma.siteSettings.upsert({
        where: { id: "global_settings" }, 
        update: { activeThemeId: themeId },
        create: { id: "global_settings", activeThemeId: themeId },
    });
    return { success: true };
  } catch (error) {
    console.error(`Error writing site settings:`, error);
    return { success: false, error: error instanceof Error ? error.message : 'Failed to set active theme.' };
  }
}

export async function updateThemeSettings(
  themeId: string, 
  newSettings: ThemeSettings
): Promise<{ success: boolean; themes?: AppTheme[]; error?: string; fieldErrors?: Partial<Record<keyof ThemeSettings, string[]>> }> {
  if (!themeId) return { success: false, error: "Theme ID is required." };

  const validationResult = themeSettingsSchema.safeParse(newSettings);
  if (!validationResult.success) {
    return { 
      success: false, 
      error: "Validation failed. Please check the HSL color values.",
      fieldErrors: validationResult.error.flatten().fieldErrors as Partial<Record<keyof ThemeSettings, string[]>>
    };
  }

  const validatedSettings = validationResult.data;

  try {
    await prisma.appTheme.update({
        where: { id: themeId },
        data: {
            settingsBackground: validatedSettings.background,
            settingsForeground: validatedSettings.foreground,
            settingsPrimary: validatedSettings.primary,
            settingsPrimaryForeground: validatedSettings.primaryForeground,
            settingsSecondary: validatedSettings.secondary,
            settingsSecondaryForeground: validatedSettings.secondaryForeground,
            settingsAccent: validatedSettings.accent,
            settingsAccentForeground: validatedSettings.accentForeground,
        }
    });
    const updatedThemes = await getAvailableThemes(); 
    return { success: true, themes: updatedThemes };
  } catch (error) {
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') { 
        return { success: false, error: `Theme with ID "${themeId}" not found.` };
    }
    console.error(`Error updating theme settings for ID "${themeId}":`, error);
    return { success: false, error: error instanceof Error ? error.message : 'Failed to update theme settings.' };
  }
}

export async function deleteTheme(themeId: string): Promise<{ success: boolean; themes?: AppTheme[]; error?: string }> {
  if (!themeId) return { success: false, error: "Theme ID is required." };
  if (themeId === 'default') {
    return { success: false, error: "The default theme cannot be deleted." };
  }

  try {
    const globalActiveThemeId = await getActiveThemeId();
    if (themeId === globalActiveThemeId) {
      return { success: false, error: "Cannot delete the currently active global theme. Please switch to another theme first." };
    }

    await prisma.appTheme.delete({
        where: { id: themeId }
    });
    const updatedThemes = await getAvailableThemes();
    return { success: true, themes: updatedThemes };
  } catch (error) {
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return { success: false, error: `Theme with ID "${themeId}" not found for deletion.` };
    }
    console.error(`Error deleting theme with ID "${themeId}":`, error);
    return { success: false, error: error instanceof Error ? error.message : 'Failed to delete theme.' };
  }
}

export async function createTheme(
  themeName: string,
  themeSettings: ThemeSettings
): Promise<{ success: boolean; themes?: AppTheme[]; error?: string; fieldErrors?: Partial<Record<keyof z.infer<typeof newThemeFormSchema>, string[] | undefined> & { settings?: Partial<Record<keyof ThemeSettings, string[]>> }> }> {
  const validationResult = newThemeFormSchema.safeParse({ name: themeName, settings: themeSettings });

  if (!validationResult.success) {
    const flattenedErrors = validationResult.error.flatten();
    const fieldErrors: any = {};
    if (flattenedErrors.fieldErrors.name) fieldErrors.name = flattenedErrors.fieldErrors.name;
    
    const settingsErrors = flattenedErrors.fieldErrors.settings as unknown as Partial<Record<keyof ThemeSettings, string[]>> | undefined;
    if (settingsErrors) {
        fieldErrors.settings = settingsErrors;
    }

    return {
      success: false,
      error: "Validation failed. Please check the theme name and color values.",
      fieldErrors: fieldErrors,
    };
  }

  const { name: validatedName, settings: validatedSettings } = validationResult.data;

  try {
    const existingTheme = await prisma.appTheme.findFirst({
        where: { name: { equals: validatedName, mode: 'insensitive' } }
    });

    if (existingTheme) {
      return {
        success: false,
        error: "A theme with this name already exists.",
        fieldErrors: { name: ["A theme with this name already exists."] }
      };
    }

    const newThemeId = validatedName.toLowerCase().replace(/\s+/g, '-') + '-' + Date.now().toString(36).slice(-4);

    await prisma.appTheme.create({
        data: {
            id: newThemeId,
            name: validatedName,
            settingsBackground: validatedSettings.background,
            settingsForeground: validatedSettings.foreground,
            settingsPrimary: validatedSettings.primary,
            settingsPrimaryForeground: validatedSettings.primaryForeground,
            settingsSecondary: validatedSettings.secondary,
            settingsSecondaryForeground: validatedSettings.secondaryForeground,
            settingsAccent: validatedSettings.accent,
            settingsAccentForeground: validatedSettings.accentForeground,
        }
    });
    const updatedThemes = await getAvailableThemes();
    return { success: true, themes: updatedThemes };
  } catch (error) {
    console.error(`Error creating theme "${validatedName}":`, error);
    return { success: false, error: error instanceof Error ? error.message : 'Failed to create theme.' };
  }
}
