// @ts-nocheck
'use server';

import type { ActivityLogEntry } from '@/types';
import prisma from './prisma';
import { ActivityType, Prisma } from '@prisma/client'; // Import Prisma for Decimal and ActivityType


export async function getUserActivity(userId: string, limit: number = 5): Promise<ActivityLogEntry[]> {
  if (!userId) {
    return [];
  }
  try {
    const userActivitiesFromDb = await prisma.activityLogEntry.findMany({
      where: {
        OR: [
          { userId: userId },
          { type: ActivityType.GENERIC_NOTIFICATION } 
        ]
      },
      orderBy: { timestamp: 'desc' },
      take: limit,
    });
    
    return userActivitiesFromDb.map(activity => ({
      ...activity,
      timestamp: activity.timestamp.toISOString(),
    }));
  } catch (error) {
    console.error(`Error fetching activity log for user ${userId}:`, error);
    return [];
  }
}

export async function logActivity(
  userId: string,
  type: ActivityType, 
  description: string,
  icon?: string 
): Promise<{ success: boolean, activity?: ActivityLogEntry; error?: string }> {
  if (!userId || !type || !description) {
    return { success: false, error: "User ID, type, and description are required to log activity." };
  }
  try {
    const newActivityFromDb = await prisma.activityLogEntry.create({
      data: {
        userId,
        timestamp: new Date(),
        type,
        description,
        icon,
      }
    });
    return { 
      success: true,
      activity: {
        ...newActivityFromDb,
        timestamp: newActivityFromDb.timestamp.toISOString(),
      } 
    };
  } catch (error) {
    console.error("Error logging activity:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to log activity.";
    return { success: false, error: errorMessage };
  }
}
