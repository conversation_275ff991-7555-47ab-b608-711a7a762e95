
"use client";

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FraudRiskTable } from "@/components/admin/FraudRiskTable";
import type { Order } from "@/types";
import { ShieldCheck, RefreshCw, Loader2 } from "lucide-react";
// import type { Metadata } from 'next'; // Cannot use Metadata in client components directly
import { getOrdersForFraudReview } from "@/lib/actions/adminActions";
import { useToast } from '@/hooks/use-toast';

// Metadata should be handled by a parent server component or layout if needed for client components
// export const metadata: Metadata = {
//   title: 'Fraud Detection - Admin Panel',
//   description: 'Analyze and manage order fraud risks.',
// };

export default function FraudDetectionPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  const fetchOrders = useCallback(async () => {
    setIsLoading(true);
    try {
      const fetchedOrders = await getOrdersForFraudReview();
      setOrders(fetchedOrders);
    } catch (error) {
      console.error("Failed to fetch orders for fraud review:", error);
      toast({
        title: "Error Fetching Orders",
        description: "Could not load orders for fraud review.",
        variant: "error",
      });
      setOrders([]); // Set to empty array on error
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center space-x-3">
          <ShieldCheck className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold text-primary">AI-Powered Fraud Detection</h1>
        </div>
        <Button variant="outline" onClick={fetchOrders} disabled={isLoading}>
          <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh Orders
        </Button>
      </div>
      
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle>Order Risk Analysis</CardTitle>
          <CardDescription>
            Review new and existing orders. Use the AI tool to assess their fraud risk level. 
            Orders flagged as high-risk may require manual review or cancellation.
            Order data is sourced from orders.json.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="ml-2 text-muted-foreground">Loading orders...</p>
            </div>
          ) : (
            <FraudRiskTable orders={orders} />
          )}
        </CardContent>
      </Card>

      <Card className="shadow-md">
          <CardHeader>
              <CardTitle>How it Works</CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-muted-foreground space-y-2">
              <p>The AI fraud detection tool analyzes various factors associated with each order, including user data (email, billing address), order details (plan, amount, IP address), and payment method.</p>
              <p>Based on this analysis, it provides a fraud risk score (0 to 1, higher is riskier), a binary fraud assessment (Yes/No), a list of contributing risk factors, and a recommended action (Approve, Review, Reject).</p>
              <p>Click the "Analyze Risk" button next to an order to initiate the AI analysis. Results will be displayed in the table. Orders with high risk scores or suspicious patterns should be carefully reviewed.</p>
          </CardContent>
      </Card>

    </div>
  );
}
