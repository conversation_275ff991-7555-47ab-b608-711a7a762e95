# **App Name**: VerityHost

## Core Features:

- Informational Website: Display VPS offerings, payment methods, and legal information for users to explore and utilize before committing to a purchase.
- User Authentication and Management: Users gain secure access to their account dashboards for managing VPS orders, payment history, and account settings.
- UPI Payment Page: Display an interface to show QR codes and unique codes; a field for entering UTR numbers. After submission, confirmation alerts will appear. Error handling ensures smooth user experience throughout the submission process.
- AI-Powered Fraud Detection Tool: Integrate an AI-powered tool within the admin panel to assess the risk level associated with each new order based on user data and order details. The AI tool analyzes multiple factors to identify potentially fraudulent orders and highlights them for review, thus ensuring transaction safety and security.

## Style Guidelines:

- Primary color: Midnight Blue (#2c3e50) for a professional and modern feel.
- Background color: Off-White (#f0f0f0) to provide a clean and spacious layout.
- Accent color: Teal (#008080) for interactive elements and highlights, providing a touch of sophistication.
- Use a professional and easily readable font like 'Open Sans' or 'Roboto' for a modern and clean design.
- Employ a set of consistent and minimalistic line icons to maintain a professional and uncluttered user interface.
- Ensure a well-structured layout with clear sections and whitespace to improve navigation and comprehension.