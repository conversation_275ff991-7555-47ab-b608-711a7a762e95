
import Link from 'next/link';
import { Logo } from '@/components/shared/Logo';

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="border-t border-border/40 bg-background">
      <div className="container mx-auto max-w-screen-2xl px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <Logo />
            <p className="mt-4 text-sm text-muted-foreground">
              Reliable VPS hosting solutions for your growing needs.
            </p>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Quick Links</h3>
            <ul className="mt-4 space-y-2">
              <li><Link href="/vps-offerings" className="text-sm text-muted-foreground hover:text-accent">VPS Offerings</Link></li>
              <li><Link href="/payment-methods" className="text-sm text-muted-foreground hover:text-accent">Payment Methods</Link></li>
              <li><Link href="/payment/upi" className="text-sm text-muted-foreground hover:text-accent">UPI Payment</Link></li>
              <li><Link href="/dashboard" className="text-sm text-muted-foreground hover:text-accent">My Account</Link></li>
              <li><Link href="/support" className="text-sm text-muted-foreground hover:text-accent">Support</Link></li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Legal</h3>
            <ul className="mt-4 space-y-2">
              <li><Link href="/legal/tos" className="text-sm text-muted-foreground hover:text-accent">Terms of Service</Link></li>
              <li><Link href="/legal/privacy" className="text-sm text-muted-foreground hover:text-accent">Privacy Policy</Link></li>
            </ul>
          </div>
        </div>
        <div className="mt-12 border-t border-border/40 pt-8 text-center">
          <p className="text-sm text-muted-foreground">
            &copy; {currentYear} SkyHosting. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
