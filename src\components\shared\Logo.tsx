import type { SVGProps } from 'react';
import Link from 'next/link';

// A simple placeholder logo. Replace with actual SVG or image if available.
const SkyHostingIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M12 2L2 7l10 5 10-5-10-5z" />
    <path d="M2 17l10 5 10-5" />
    <path d="M2 12l10 5 10-5" />
  </svg>
);


export function Logo({ className }: { className?: string }) {
  return (
    <Link href="/" className={`flex items-center space-x-2 text-primary ${className}`}>
      <SkyHostingIcon className="h-8 w-8 text-accent" />
      <span className="text-2xl font-bold">SkyHosting</span>
    </Link>
  );
}
