// @ts-nocheck
'use server';

import type { User } from '@/types';
import prisma from './prisma';
import type { UserRole, UserStatus } from '@prisma/client'; // Import UserRole and UserStatus types

export async function getAllUsers(): Promise<User[]> {
  try {
    const usersFromDb = await prisma.user.findMany();
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    return usersFromDb.map(({ password, ...userWithoutPassword }) => userWithoutPassword);
  } catch (error) {
    console.error("Error fetching all users:", error);
    return [];
  }
}

export async function updateUserStatus(userId: string, newStatus: User['status']): Promise<{ success: boolean, user?: User, error?: string }> {
  if (!userId) {
    return { success: false, error: "User ID is required." };
  }
  if (!newStatus || !['ACTIVE', 'SUSPENDED', 'PENDING_VERIFICATION'].includes(newStatus.toUpperCase())) {
    return { success: false, error: "Invalid status provided." };
  }

  try {
    const updatedUserFromDb = await prisma.user.update({
      where: { id: userId },
      data: { status: newStatus.toUpperCase() as UserStatus },
    });
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...updatedUser } = updatedUserFromDb;
    return { success: true, user: updatedUser };
  } catch (error) {
    if (error.code === 'P2025') {
        return { success: false, error: 'User not found.' };
    }
    console.error("Error updating user status:", error);
    return { success: false, error: 'Failed to update user status.' };
  }
}

export async function updateUserRole(userId: string, newRole: User['role']): Promise<{ success: boolean, user?: User, error?: string }> {
  if (!userId) {
    return { success: false, error: "User ID is required." };
  }
  if (!newRole || !['USER', 'ADMIN'].includes(newRole.toUpperCase())) {
    return { success: false, error: "Invalid role provided." };
  }
 try {
    const updatedUserFromDb = await prisma.user.update({
        where: { id: userId },
        data: { role: newRole.toUpperCase() as UserRole },
    });
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...updatedUser } = updatedUserFromDb;
    return { success: true, user: updatedUser };
 } catch (error) {
    if (error.code === 'P2025') {
        return { success: false, error: 'User not found.' };
    }
    console.error("Error updating user role:", error);
    return { success: false, error: 'Failed to update user role.' };
 }
}

export async function deleteUser(userId: string): Promise<{ success: boolean, error?: string }> {
  if (!userId) {
    return { success: false, error: "User ID is required." };
  }
  try {
    const userToDelete = await prisma.user.findUnique({ where: { id: userId } });
    if (userToDelete?.role === 'ADMIN') {
        const adminCount = await prisma.user.count({ where: { role: 'ADMIN' } });
        if (adminCount <= 1) {
            return { success: false, error: "Cannot delete the last admin user." };
        }
    }
    await prisma.user.delete({ where: { id: userId } });
    return { success: true };
  } catch (error) {
    if (error.code === 'P2025') { 
        return { success: false, error: 'User not found for deletion.' };
    }
    console.error("Error deleting user:", error);
    return { success: false, error: 'Failed to delete user.' };
  }
}

export async function updateUser(userId: string, updates: { name?: string; email?: string }): Promise<{ success: boolean, user?: User, error?: string, fieldErrors?: Record<string, string[]> }> {
  if (!userId) {
    return { success: false, error: "User ID is required." };
  }
  if (!updates || (updates.name === undefined && updates.email === undefined)) {
    return { success: false, error: "No updates provided." };
  }

  // Basic validation for name and email if provided
  if (updates.name !== undefined && (updates.name.length < 2 || updates.name.length > 50)) {
    return { success: false, error: "Validation failed.", fieldErrors: { name: ["Name must be between 2 and 50 characters."]}};
  }
  if (updates.email !== undefined && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(updates.email)) {
     return { success: false, error: "Validation failed.", fieldErrors: { email: ["Invalid email address format."]}};
  }


  try {
    if (updates.email) {
        const existingUser = await prisma.user.findFirst({
            where: {
                email: updates.email,
                NOT: { id: userId }
            }
        });
        if (existingUser) {
            return { success: false, error: 'This email address is already in use by another account.', fieldErrors: { email: ['This email address is already in use.']}};
        }
    }

    const updatedUserFromDb = await prisma.user.update({
        where: { id: userId },
        data: updates,
    });
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...updatedUserWithoutPassword } = updatedUserFromDb;
    return { success: true, user: updatedUserWithoutPassword };
  } catch (error) {
     if (error.code === 'P2025') {
        return { success: false, error: 'User not found.' };
    }
    console.error("Error updating user details:", error);
    return { success: false, error: 'Failed to update user details.' };
  }
}
