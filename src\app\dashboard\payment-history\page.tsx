
"use client";

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { Payment } from "@/types";
import { Download, CreditCard, Loader2, ExternalLink } from "lucide-react";
import Link from "next/link";
import { useAuth } from '@/hooks/useAuth';
import { getUserPayments } from '@/lib/actions/paymentActions';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';

export default function PaymentHistoryPage() {
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchPayments() {
      if (isAuthenticated && user?.id) {
        setIsLoading(true);
        try {
          const fetchedPayments = await getUserPayments(user.id);
          setPayments(fetchedPayments);
        } catch (error) {
          console.error("Failed to fetch payments:", error);
          toast({
            title: "Error",
            description: "Could not load your payment history.",
            variant: "error",
          });
          setPayments([]);
        } finally {
          setIsLoading(false);
        }
      } else if (isAuthenticated === false) {
        setIsLoading(false);
        setPayments([]);
      }
    }
    fetchPayments();
  }, [user, isAuthenticated, toast]);

  const getStatusBadgeVariant = (status: Payment['status']) => {
    switch (status) {
      case 'completed': return 'default';
      case 'pending': return 'secondary';
      case 'failed': return 'destructive';
      default: return 'outline';
    }
  };

  const handleDownloadInvoice = (paymentId: string) => {
    const payment = payments.find(p => p.id === paymentId);
    if (!payment || payment.status !== 'completed') {
      toast({
        title: "Invoice Not Available",
        description: "Invoice can only be downloaded for completed payments.",
        variant: "warning",
      });
      return;
    }

    const invoiceHTML = `
      <html>
        <head>
          <title>Invoice - ${payment.id}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; color: #333; }
            .container { width: 100%; max-width: 800px; margin: auto; padding: 20px; border: 1px solid #eee; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
            h1, h2 { color: #2c3e50; }
            h1 { text-align: center; border-bottom: 2px solid #2c3e50; padding-bottom: 10px; margin-bottom: 20px;}
            .header-flex { display: flex; justify-content: space-between; margin-bottom: 30px; }
            .header-flex div { flex-basis: 48%; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
            th { background-color: #f2f2f2; }
            .total-row td { font-weight: bold; }
            .footer { text-align: center; margin-top: 30px; font-size: 0.9em; color: #777; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>Invoice</h1>
            <div class="header-flex">
              <div>
                <h2>SkyHosting</h2>
                <p><EMAIL></p>
              </div>
              <div>
                <p><strong>Invoice ID:</strong> ${payment.id}</p>
                <p><strong>Order ID:</strong> ${payment.orderId}</p>
                <p><strong>Date:</strong> ${new Date(payment.paymentDate).toLocaleDateString()}</p>
              </div>
            </div>
            <div>
              <h3>Billed To:</h3>
              <p>${user?.name || user?.email || 'Valued Customer'}</p>
              ${user?.email ? `<p>${user.email}</p>` : ''}
            </div>
            <table>
              <thead>
                <tr>
                  <th>Description</th>
                  <th>Amount</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>VPS Service - Order ${payment.orderId}</td>
                  <td>₹${payment.amount.toFixed(2)}</td>
                </tr>
                <tr class="total-row">
                  <td>Total Amount Paid</td>
                  <td>₹${payment.amount.toFixed(2)}</td>
                </tr>
              </tbody>
            </table>
            <div style="margin-top: 20px;">
              <p><strong>Payment Method:</strong> ${payment.method}</p>
              <p><strong>Transaction ID:</strong> ${payment.transactionId || 'N/A'}</p>
              <p><strong>Status:</strong> <span style="color: green; font-weight: bold;">PAID</span></p>
            </div>
            <div class="footer">
              <p>Thank you for your business!</p>
              <p>&copy; ${new Date().getFullYear()} SkyHosting</p>
            </div>
          </div>
        </body>
      </html>
    `;

    const newTab = window.open();
    if (newTab) {
      newTab.document.open();
      newTab.document.write(invoiceHTML);
      newTab.document.close();
    } else {
      toast({
        title: "Popup Blocked",
        description: "Please allow popups for this site to view the invoice.",
        variant: "warning",
      });
    }
  };

  const renderSkeletonPaymentCards = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {[...Array(4)].map((_, i) => (
        <Card key={i} className="shadow-md flex flex-col">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <Skeleton className="h-5 w-3/4 mb-1" />
                <Skeleton className="h-3 w-1/2" />
              </div>
              <Skeleton className="h-6 w-16 rounded-full" />
            </div>
          </CardHeader>
          <CardContent className="space-y-3 text-sm flex-grow">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/4 mb-1 sm:mb-0" />
              <Skeleton className="h-4 w-1/2" />
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/5 mb-1 sm:mb-0" />
              <Skeleton className="h-4 w-2/3" />
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/6 mb-1 sm:mb-0" />
              <Skeleton className="h-6 w-1/3" />
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-baseline">
              <Skeleton className="h-4 w-1/3 mb-1 sm:mb-0" />
              <Skeleton className="h-4 w-1/3" />
            </div>
          </CardContent>
          <CardFooter className="border-t pt-4 flex justify-end items-center gap-2">
            <Skeleton className="h-8 w-24" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );

  if (isLoading) {
    return (
       <div className="space-y-6">
        <h1 className="text-3xl font-bold text-primary">Payment History</h1>
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>Your Transaction Records</CardTitle>
            <CardDescription>View all your past payments and download invoices. Data is sourced from payments.json.</CardDescription>
          </CardHeader>
          <CardContent>
            {renderSkeletonPaymentCards()}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold text-primary">Payment History</h1>

      <Card className="shadow-md">
        <CardHeader>
          <CardTitle>Your Transaction Records</CardTitle>
          <CardDescription>View all your past payments and download invoices. Data is sourced from payments.json.</CardDescription>
        </CardHeader>
        <CardContent>
          {payments.length === 0 ? (
            <div className="text-center py-12">
              <CreditCard className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold text-primary mb-2">No Payment History</h3>
              <p className="text-muted-foreground mb-4">You have no recorded payments yet.</p>
              <Button asChild>
                <Link href="/vps-offerings">Make a Purchase</Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {payments.map((payment) => (
                <Card key={payment.id} className="shadow-sm hover:shadow-md transition-shadow flex flex-col">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                        <div>
                            <CardTitle className="text-lg font-semibold text-primary">
                                Payment ID: <span className="font-mono text-base">{payment.id}</span>
                            </CardTitle>
                            <CardDescription className="text-xs">
                                Order ID: <span className="font-mono text-xs">{payment.orderId}</span>
                            </CardDescription>
                        </div>
                         <Badge variant={getStatusBadgeVariant(payment.status)} className="capitalize text-xs px-2 py-1">
                            {payment.status}
                        </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-2 text-sm flex-grow">
                    <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                      <span className="text-sm text-muted-foreground">Date:</span>
                      <span className="text-sm font-medium break-words">{new Date(payment.paymentDate).toLocaleString()}</span>
                    </div>
                    <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                      <span className="text-sm text-muted-foreground">Amount:</span>
                      <span className="text-sm font-bold text-xl text-accent break-words">₹{payment.amount.toFixed(2)}</span>
                    </div>
                    <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                      <span className="text-sm text-muted-foreground">Method:</span>
                      <span className="text-sm font-medium break-words">{payment.method}</span>
                    </div>
                    <div className="flex flex-col text-left sm:flex-row sm:justify-between sm:items-baseline">
                      <span className="text-sm text-muted-foreground">Transaction ID:</span>
                      <span className="text-sm font-medium break-words font-mono text-xs truncate max-w-[150px] sm:max-w-xs" title={payment.transactionId || 'N/A'}>
                        {payment.transactionId || 'N/A'}
                      </span>
                    </div>
                  </CardContent>
                  <CardFooter className="border-t pt-4 flex justify-end items-center gap-2">
                    {payment.status === 'completed' && (
                      <Button variant="outline" size="sm" onClick={() => handleDownloadInvoice(payment.id)}>
                        <Download className="mr-2 h-4 w-4" /> Download Invoice
                      </Button>
                    )}
                    {payment.status === 'pending' && (
                      <Button variant="default" size="sm" asChild>
                          <Link href={`/payment/upi?orderId=${payment.orderId}&planId=${payments.find(p=>p.id === payment.id)?.orderId?.includes("ORD") ? '' : payments.find(p=>p.id === payment.id)?.orderId?.replace('PLAN-','') }`}> {/* A bit complex to get planId if not in order obj */}
                            <ExternalLink className="mr-2 h-4 w-4" /> Complete Payment
                          </Link>
                      </Button>
                    )}
                    {payment.status === 'failed' && (
                        <span className="text-xs text-destructive italic">Payment Failed</span>
                    )}
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
