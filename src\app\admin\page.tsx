
"use client"; 

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users, ShieldAlert, ShoppingBag, AlertTriangle, Ticket as TicketIcon, Loader2 } from "lucide-react"; 
import Link from "next/link";
import { getAllUsers } from '@/lib/actions/userManagementActions';
import { getOrdersForFraudReview } from '@/lib/actions/adminActions';
import { countAllTicketsByStatus } from '@/lib/actions/ticketActions';
import type { Order } from "@/types";
import { useEffect, useState } from "react";

interface DashboardStats {
  totalUsers: number;
  totalOrders: number;
  pendingFraudChecks: number;
  highRiskOrders: number;
  openSupportTickets: number;
}

export default function AdminDashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchDashboardStats() {
      setIsLoading(true);
      try {
        const users = await getAllUsers();
        const orders = await getOrdersForFraudReview();
        const openTicketsCount = await countAllTicketsByStatus(['open', 'in_progress']);

        const totalUsers = users.length;
        const totalOrders = orders.length;
        const pendingFraudChecks = orders.filter((order: Order) => order.status === 'pending').length;
        const highRiskOrders = orders.filter((order: Order) => order.fraudAnalysis?.isFraudulent === true).length;

        setStats({
          totalUsers,
          totalOrders,
          pendingFraudChecks,
          highRiskOrders,
          openSupportTickets: openTicketsCount,
        });
      } catch (error) {
        console.error("Failed to fetch dashboard stats:", error);
        setStats({ totalUsers: 0, totalOrders: 0, pendingFraudChecks: 0, highRiskOrders: 0, openSupportTickets: 0 });
      } finally {
        setIsLoading(false);
      }
    }
    fetchDashboardStats();
  }, []);

  if (isLoading || !stats) {
    return (
      <div className="space-y-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-primary">Admin Dashboard</h1>
            <p className="text-muted-foreground">Loading statistics...</p>
          </div>
        </div>
         <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
            {[
                { title: "Total Users", icon: Users, colorClass: "text-primary" },
                { title: "Total Orders", icon: ShoppingBag, colorClass: "text-primary" },
                { title: "Open Tickets", icon: TicketIcon, colorClass: "text-primary" },
                { title: "Pending Orders", icon: ShieldAlert, colorClass: "text-yellow-700", bgClass: "bg-yellow-50 border-yellow-200" },
                { title: "High-Risk Orders", icon: AlertTriangle, colorClass: "text-red-700", bgClass: "bg-red-50 border-red-200" }
            ].map((item, index) => (
                <Card key={index} className={`shadow-sm ${item.bgClass || ''}`}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className={`text-sm font-medium ${item.colorClass}`}>{item.title}</CardTitle>
                        <item.icon className={`h-5 w-5 ${item.colorClass === 'text-primary' ? 'text-accent' : item.colorClass.replace('text-', 'text-').replace('-700', '-600')}`} />
                    </CardHeader>
                    <CardContent>
                        <div className={`text-2xl font-bold ${item.colorClass}`}><Loader2 className="inline-block h-6 w-6 animate-spin" /></div>
                        <p className={`text-xs ${item.colorClass === 'text-primary' ? 'text-muted-foreground' : item.colorClass.replace('-700', '-600')}`}>
                            Loading...
                        </p>
                        <Button variant="link" size="sm" className="mt-2 p-0 h-auto" disabled>Loading...</Button>
                    </CardContent>
                </Card>
            ))}
        </div>
         <Card className="shadow-sm">
            <CardHeader>
                <CardTitle className="text-xl text-primary">System Health</CardTitle>
                <CardDescription>Current status of critical systems.</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="space-y-3">
                {[1,2,3].map(i => (
                    <div key={i} className="flex items-center justify-between p-3 bg-muted/50 border border-border rounded-md">
                        <p className="text-sm font-medium text-muted-foreground">Loading system status...</p>
                        <Loader2 className="w-3 h-3 animate-spin text-muted-foreground"/>
                    </div>
                ))}
                </div>
            </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-primary">Admin Dashboard</h1>
          <p className="text-muted-foreground">Overview of SkyHosting operations.</p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5"> {/* Adjusted grid for 5 cards */}
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-5 w-5 text-accent" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">{stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              Registered users on the platform.
            </p>
             <Button variant="link" size="sm" className="mt-2 p-0 h-auto" asChild>
              <Link href="/admin/users" legacyBehavior passHref>
                <a>Manage Users</a>
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingBag className="h-5 w-5 text-accent" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">{stats.totalOrders}</div>
            <p className="text-xs text-muted-foreground">
              All orders placed.
            </p>
             <Button variant="link" size="sm" className="mt-2 p-0 h-auto" asChild>
               <Link href="/admin/fraud-detection" legacyBehavior passHref>
                <a>View Orders</a>
               </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Open Tickets</CardTitle>
            <TicketIcon className="h-5 w-5 text-accent" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">{stats.openSupportTickets}</div>
            <p className="text-xs text-muted-foreground">
              Active support requests.
            </p>
             <Button variant="link" size="sm" className="mt-2 p-0 h-auto" asChild>
               <Link href="/admin/tickets" legacyBehavior passHref>
                <a>Manage Tickets</a>
               </Link>
            </Button>
          </CardContent>
        </Card>
        
        <Card className="shadow-sm hover:shadow-md transition-shadow bg-yellow-50 border-yellow-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-yellow-700">Pending Orders</CardTitle>
            <ShieldAlert className="h-5 w-5 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-700">{stats.pendingFraudChecks}</div>
            <p className="text-xs text-yellow-600">
              Orders with 'pending' status.
            </p>
            <Button variant="link" size="sm" className="mt-2 p-0 h-auto text-yellow-700 hover:text-yellow-800" asChild>
              <Link href="/admin/fraud-detection?filter=pending" legacyBehavior passHref>
                <a>Review Orders</a>
              </Link>
            </Button>
          </CardContent>
        </Card>

         <Card className="shadow-sm hover:shadow-md transition-shadow bg-red-50 border-red-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-700">High-Risk Orders</CardTitle>
            <AlertTriangle className="h-5 w-5 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-700">{stats.highRiskOrders}</div>
            <p className="text-xs text-red-600">
              Orders flagged as high-risk by AI.
            </p>
            <Button variant="link" size="sm" className="mt-2 p-0 h-auto text-red-700 hover:text-red-800" asChild>
              <Link href="/admin/fraud-detection?filter=high-risk" legacyBehavior passHref>
                <a>View High-Risk</a>
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="text-xl text-primary">System Health</CardTitle>
          <CardDescription>Current status of critical systems.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm font-medium text-green-700">Payment Gateway: Operational</p>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm font-medium text-green-700">VPS Provisioning: Operational</p>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm font-medium text-green-700">AI Fraud Detection Service: Operational</p>
                 <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

