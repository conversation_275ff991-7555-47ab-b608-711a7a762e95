// @ts-nocheck
'use server';

import type { VPSPlan } from '@/types';
import prisma from './prisma';
import * as z from "zod";
import { Prisma, VpsPlanSpecialTag } from '@prisma/client'; // VpsPlanSpecialTag is the enum value object
import type { VpsPlanSpecialTag as VpsPlanSpecialTagType } from '@prisma/client'; // VpsPlanSpecialTagType is the type

// This array is used to populate the Select dropdown on the client-side for choosing a special tag.
// It includes an empty string for "None" and all possible enum values.
const specialTagOptionsServer: (VpsPlanSpecialTagType | "" | undefined)[] = [
    "", 
    VpsPlanSpecialTag.MOST_BOUGHT, 
    VpsPlanSpecialTag.BEST_PRICE, 
    VpsPlanSpecialTag.NEW, 
    VpsPlanSpecialTag.RECOMMENDED, 
    VpsPlanSpecialTag.SALE
];


const planValidationSchema = z.object({
  name: z.string().min(3, { message: "Plan name must be at least 3 characters." }).max(50, { message: "Plan name cannot exceed 50 characters." }),
  description: z.string().min(10, { message: "Description must be at least 10 characters." }).max(500, { message: "Description cannot exceed 500 characters." }),
  pricePerMonth: z.coerce.number().positive({ message: "Price must be a positive number." }),
  originalPricePerMonth: z.union([z.coerce.number().positive().optional(), z.literal('')]).optional().transform(val => val === '' ? undefined : val),
  discountLabel: z.string().max(20, { message: "Discount label cannot exceed 20 characters." }).optional().transform(val => val === '' ? undefined : val),
  specialTag: z.nativeEnum(VpsPlanSpecialTag).optional().or(z.literal("").transform(() => undefined)) as z.ZodType<VpsPlanSpecialTagType | undefined>,
  cpuCores: z.coerce.number().int({ message: "CPU Cores must be an integer." }).positive({ message: "CPU Cores must be a positive integer." }),
  ramGB: z.coerce.number().positive({ message: "RAM must be a positive number." }), // No Math.floor here, Prisma handles Float
  storageGB: z.coerce.number().int({ message: "Storage must be an integer." }).positive({ message: "Storage must be a positive integer." }),
  bandwidthTB: z.coerce.number().positive({ message: "Bandwidth must be a positive number." }),
  features: z.string().min(1, { message: "Please list at least one feature (each on a new line)." }),
});

export interface PlanActionResult {
  success: boolean;
  plan?: VPSPlan;
  error?: string;
  fieldErrors?: Partial<Record<keyof z.infer<typeof planValidationSchema>, string[]>>;
}


export async function getAllVpsPlans(): Promise<VPSPlan[]> {
  try {
    const plansFromDb = await prisma.vPSPlan.findMany({
        orderBy: { pricePerMonth: 'asc' }
    });
    return plansFromDb.map(p => ({
        ...p,
        pricePerMonth: p.pricePerMonth.toNumber(), // Convert Decimal to number
        originalPricePerMonth: p.originalPricePerMonth?.toNumber(), // Convert Decimal to number
        ramGB: p.ramGB, // Already number (Float from Prisma)
        storageGB: p.storageGB, // Already number (Int from Prisma)
        bandwidthTB: p.bandwidthTB, // Already number (Float from Prisma)
        features: p.features || [],
    }));
  } catch (error) {
    console.error('Error in getAllVpsPlans:', error);
    return [];
  }
}

export async function getVpsPlanById(planId: string): Promise<VPSPlan | undefined> {
  if (!planId) return undefined;
  try {
    const planFromDb = await prisma.vPSPlan.findUnique({
        where: { id: planId }
    });
    if (!planFromDb) return undefined;
    return {
        ...planFromDb,
        pricePerMonth: planFromDb.pricePerMonth.toNumber(),
        originalPricePerMonth: planFromDb.originalPricePerMonth?.toNumber(),
        ramGB: planFromDb.ramGB,
        storageGB: planFromDb.storageGB,
        bandwidthTB: planFromDb.bandwidthTB,
        features: planFromDb.features || [],
    };
  } catch (error) {
    console.error(`Error fetching plan by ID ${planId}:`, error);
    return undefined;
  }
}

type ClientPlanFormData = z.infer<typeof planValidationSchema>;

export async function createVpsPlan(planDataFromClient: ClientPlanFormData): Promise<PlanActionResult> {
  const validationResult = planValidationSchema.safeParse(planDataFromClient);
  if (!validationResult.success) {
    const fieldErrors = validationResult.error.flatten().fieldErrors as Partial<Record<keyof ClientPlanFormData, string[]>>;
    return {
      success: false,
      error: "Validation failed. Please check the fields below.",
      fieldErrors: fieldErrors
    };
  }

  const validatedData = validationResult.data;

  try {
    const existingPlanByName = await prisma.vPSPlan.findFirst({
        where: { name: { equals: validatedData.name, mode: 'insensitive' } }
    });
    if (existingPlanByName) {
       return {
        success: false,
        error: "Validation failed.",
        fieldErrors: { name: ["A plan with this name already exists."] }
      };
    }

    const newPlanId = (validatedData.name).toLowerCase().replace(/\s+/g, '-') + '-' + Date.now().toString(36).slice(-4);

    const createdPlanFromDb = await prisma.vPSPlan.create({
        data: {
            id: newPlanId,
            name: validatedData.name,
            description: validatedData.description,
            pricePerMonth: validatedData.pricePerMonth, // Prisma handles number to Decimal
            originalPricePerMonth: validatedData.originalPricePerMonth, // Prisma handles number to Decimal or undefined
            discountLabel: validatedData.discountLabel,
            specialTag: validatedData.specialTag, // Prisma handles string to enum or undefined
            cpuCores: validatedData.cpuCores,
            ramGB: validatedData.ramGB, // Float
            storageGB: validatedData.storageGB,
            bandwidthTB: validatedData.bandwidthTB,
            features: validatedData.features.split('\\n').map(f => f.trim()).filter(f => f.length > 0),
        }
    });
    return {
        success: true,
        plan: {
            ...createdPlanFromDb,
            pricePerMonth: createdPlanFromDb.pricePerMonth.toNumber(),
            originalPricePerMonth: createdPlanFromDb.originalPricePerMonth?.toNumber(),
            ramGB: createdPlanFromDb.ramGB,
            storageGB: createdPlanFromDb.storageGB,
            bandwidthTB: createdPlanFromDb.bandwidthTB,
            features: createdPlanFromDb.features || [],
        }
    };
  } catch (error) {
    console.error('Error creating VPS plan:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Failed to create VPS plan.' };
  }
}

export async function updateVpsPlan(planId: string, updatesFromClient: Partial<ClientPlanFormData>): Promise<PlanActionResult> {
  if (!planId) return { success: false, error: "Plan ID is required." };
  
  try {
    const currentPlan = await prisma.vPSPlan.findUnique({ where: { id: planId } });
    if (!currentPlan) {
      return { success: false, error: 'Plan not found.' };
    }

    // Prepare data for validation by merging updates with current data
    const dataToValidate = {
      name: updatesFromClient.name ?? currentPlan.name,
      description: updatesFromClient.description ?? currentPlan.description,
      pricePerMonth: updatesFromClient.pricePerMonth ?? currentPlan.pricePerMonth.toNumber(),
      originalPricePerMonth: updatesFromClient.originalPricePerMonth === '' ? undefined : (updatesFromClient.originalPricePerMonth ?? currentPlan.originalPricePerMonth?.toNumber()),
      discountLabel: updatesFromClient.discountLabel === '' ? undefined : (updatesFromClient.discountLabel ?? currentPlan.discountLabel),
      specialTag: updatesFromClient.specialTag === '' ? undefined : (updatesFromClient.specialTag ?? currentPlan.specialTag),
      cpuCores: updatesFromClient.cpuCores ?? currentPlan.cpuCores,
      ramGB: updatesFromClient.ramGB ?? currentPlan.ramGB,
      storageGB: updatesFromClient.storageGB ?? currentPlan.storageGB,
      bandwidthTB: updatesFromClient.bandwidthTB ?? currentPlan.bandwidthTB,
      features: typeof updatesFromClient.features === 'string' ? updatesFromClient.features : (currentPlan.features || []).join('\\n'),
    };

    const validationResult = planValidationSchema.safeParse(dataToValidate);
    if (!validationResult.success) {
      const fieldErrors = validationResult.error.flatten().fieldErrors as Partial<Record<keyof ClientPlanFormData, string[]>>;
      return {
        success: false,
        error: "Validation failed. Please check the fields below.",
        fieldErrors: fieldErrors
      };
    }

    const validatedData = validationResult.data;

    if (validatedData.name.toLowerCase() !== currentPlan.name.toLowerCase()) {
        const existingPlanByName = await prisma.vPSPlan.findFirst({
            where: {
                name: { equals: validatedData.name, mode: 'insensitive' },
                NOT: { id: planId }
            }
        });
        if (existingPlanByName) {
          return {
            success: false,
            error: "Validation failed.",
            fieldErrors: { name: ["Another plan with this name already exists."] }
          };
        }
    }

    const updatedPlanFromDb = await prisma.vPSPlan.update({
        where: { id: planId },
        data: {
            name: validatedData.name,
            description: validatedData.description,
            pricePerMonth: validatedData.pricePerMonth,
            originalPricePerMonth: validatedData.originalPricePerMonth,
            discountLabel: validatedData.discountLabel,
            specialTag: validatedData.specialTag,
            cpuCores: validatedData.cpuCores,
            ramGB: validatedData.ramGB,
            storageGB: validatedData.storageGB,
            bandwidthTB: validatedData.bandwidthTB,
            features: validatedData.features.split('\\n').map(f => f.trim()).filter(f => f.length > 0),
        }
    });
    return {
        success: true,
        plan: {
            ...updatedPlanFromDb,
            pricePerMonth: updatedPlanFromDb.pricePerMonth.toNumber(),
            originalPricePerMonth: updatedPlanFromDb.originalPricePerMonth?.toNumber(),
            ramGB: updatedPlanFromDb.ramGB,
            storageGB: updatedPlanFromDb.storageGB,
            bandwidthTB: updatedPlanFromDb.bandwidthTB,
            features: updatedPlanFromDb.features || [],
        }
    };
  } catch (error) {
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return { success: false, error: 'Plan not found.' };
    }
    console.error('Error updating VPS plan:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Failed to update VPS plan.' };
  }
}

export async function deleteVpsPlan(planId: string): Promise<{ success: boolean; error?: string }> {
  if (!planId) return { success: false, error: "Plan ID is required." };
  try {
    const ordersUsingPlan = await prisma.order.count({ where: { planId } });
    if (ordersUsingPlan > 0) {
      return { success: false, error: 'Cannot delete plan as it is currently used by active orders.' };
    }
    await prisma.vPSPlan.delete({ where: { id: planId } });
    return { success: true };
  } catch (error) {
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return { success: false, error: 'Plan not found for deletion.' };
    }
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2003') { // Foreign key constraint failed
        return { success: false, error: 'Cannot delete this plan because it is referenced by existing records (e.g., orders).' };
    }
    console.error('Error deleting VPS plan:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Failed to delete VPS plan.' };
  }
}
