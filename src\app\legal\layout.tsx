
import { <PERSON>, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { FileText, ShieldQuestion } from "lucide-react";

export default function LegalLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="grid md:grid-cols-4 gap-8 py-8">
      <aside className="md:col-span-1">
        <Card className="sticky top-24 shadow-sm">
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold text-primary mb-4">Legal Center</h2>
            <nav className="space-y-2">
              <Link href="/legal/tos" className="flex items-center p-2 rounded-md hover:bg-muted text-muted-foreground hover:text-primary transition-colors">
                <FileText className="w-4 h-4 mr-2" /> Terms of Service
              </Link>
              <Link href="/legal/privacy" className="flex items-center p-2 rounded-md hover:bg-muted text-muted-foreground hover:text-primary transition-colors">
                <ShieldQuestion className="w-4 h-4 mr-2" /> Privacy Policy
              </Link>
            </nav>
          </CardContent>
        </Card>
      </aside>
      <main className="md:col-span-3">
        <Card className="shadow-sm">
          <CardContent className="p-6 md:p-8">
            {children}
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
