// @ts-nocheck
'use server';

import type { Payment, PaymentStatus } from '@/types';
import { logActivity } from './activityLogActions';
import prisma from './prisma';
import { ActivityType, Prisma } from '@prisma/client'; // Import Prisma for Decimal

export async function getAllPayments(): Promise<Payment[]> {
  try {
    const allPaymentsFromDb = await prisma.payment.findMany({
      orderBy: { paymentDate: 'desc' },
    });
    return allPaymentsFromDb.map(p => ({
      ...p,
      amount: p.amount.toNumber(), 
      paymentDate: p.paymentDate.toISOString(),
    }));
  } catch (error) {
    console.error('Error fetching all payments:', error);
    return [];
  }
}

export async function updatePaymentStatus(paymentId: string, newStatus: PaymentStatus): Promise<{ success: boolean; payment?: Payment; error?: string }> {
  if (!paymentId) return { success: false, error: "Payment ID is required." };
  if (!newStatus) return { success: false, error: "New status is required." };

  try {
    const updatedPaymentFromDb = await prisma.payment.update({
      where: { id: paymentId },
      data: { status: newStatus.toUpperCase() as Prisma.PaymentStatus },
    });

    if (newStatus.toUpperCase() === 'COMPLETED') {
      await logActivity(
        updatedPaymentFromDb.userId,
        ActivityType.PAYMENT_COMPLETED, 
        `Payment ${paymentId} for order ${updatedPaymentFromDb.orderId} marked as completed.`,
        'CreditCard'
      );
    }

    return { 
      success: true, 
      payment: {
        ...updatedPaymentFromDb,
        amount: updatedPaymentFromDb.amount.toNumber(), 
        paymentDate: updatedPaymentFromDb.paymentDate.toISOString(),
      } 
    };
  } catch (error) {
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') { 
        return { success: false, error: 'Payment not found.' };
    }
    console.error('Error updating payment status:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to update payment status.';
    return { success: false, error: errorMessage };
  }
}

export async function getUserPayments(userId: string): Promise<Payment[]> {
  if (!userId) {
    console.warn('getUserPayments called without a userId.');
    return [];
  }
  try {
    const userPaymentsFromDb = await prisma.payment.findMany({
      where: { userId },
      orderBy: { paymentDate: 'desc' },
    });
    
    return userPaymentsFromDb.map(p => ({
        ...p,
        amount: p.amount.toNumber(), 
        paymentDate: p.paymentDate.toISOString(),
    }));
  } catch (error) {
    console.error(`Error fetching payments for user ${userId}:`, error);
    return [];
  }
}

interface CreatePaymentData {
  orderId: string;
  userId: string;
  amount: number; 
  method: string;
  transactionId?: string; 
  status: PaymentStatus;
}

export async function createPaymentRecord(paymentData: CreatePaymentData): Promise<{ success: boolean, payment?: Payment; error?: string }> {
  if (!paymentData.userId) { 
    return { success: false, error: "User ID is required to create a payment record." };
  }
  if (paymentData.amount <= 0 && paymentData.orderId?.startsWith("PLAN-")) { // Ensure orderId might be undefined
      return { success: false, error: "Payment amount must be greater than zero for new plan purchases."};
  }
  if (!paymentData.orderId) {
    return { success: false, error: "Order ID is required to create a payment record." };
  }
  if (!paymentData.status) {
    return { success: false, error: "Payment status is required." };
  }

  try {
    const now = new Date();
    const newPaymentFromDb = await prisma.payment.create({
      data: {
        orderId: paymentData.orderId,
        userId: paymentData.userId,
        amount: paymentData.amount, 
        method: paymentData.method,
        transactionId: paymentData.transactionId,
        status: paymentData.status.toUpperCase() as Prisma.PaymentStatus,
        paymentDate: now,
      }
    });

    await logActivity(
      paymentData.userId,
      ActivityType.PAYMENT_COMPLETED, 
      `Payment details submitted for order ${paymentData.orderId} (UTR: ${paymentData.transactionId || 'N/A'}).`,
      'CreditCard'
    );

    return { 
        success: true,
        payment: {
            ...newPaymentFromDb,
            amount: newPaymentFromDb.amount.toNumber(), 
            paymentDate: newPaymentFromDb.paymentDate.toISOString(),
        } 
    };
  } catch (error) {
    console.error("Error creating payment record:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to create payment record.";
    return { success: false, error: errorMessage };
  }
}

export async function countUserPaymentsByStatus(userId: string, status: PaymentStatus): Promise<number> {
  if (!userId) {
    return 0;
  }
  if (!status) {
    console.warn("countUserPaymentsByStatus called without status");
    return 0;
  }
  try {
    return await prisma.payment.count({
      where: { userId, status: status.toUpperCase() as Prisma.PaymentStatus },
    });
  } catch (error) {
    console.error(`Error counting payments for user ${userId} with status ${status}:`, error);
    return 0;
  }
}
