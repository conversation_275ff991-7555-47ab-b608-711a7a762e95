
"use client"

import { useToast, type ExtendedToastProps } from "@/hooks/use-toast" // Use ExtendedToastProps
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast"
import { CheckCircle, XCircle, Info, AlertTriangle } from "lucide-react"
import type React from "react"

// Define icons for each toast variant
const ICONS: Record<NonNullable<ExtendedToastProps['variant']>, React.ReactNode> = {
  success: <CheckCircle className="h-5 w-5 text-green-500" />,
  error: <XCircle className="h-5 w-5 text-red-500" />,
  info: <Info className="h-5 w-5 text-blue-500" />,
  warning: <AlertTriangle className="h-5 w-5 text-yellow-500" />,
  default: null, // No icon for default, or you can add one e.g. <Bell className="h-5 w-5" />
};


export function Toaster() {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, variant, ...props }) {
        const IconToRender = (variant && ICONS[variant]) || null;
        return (
          <Toast key={id} variant={variant} {...props}>
            <div className="flex items-start space-x-3"> {/* Container for icon and text */}
              {IconToRender && <div className="flex-shrink-0 pt-0.5">{IconToRender}</div>}
              <div className="grid gap-1 flex-grow"> {/* Text content */}
                {title && <ToastTitle>{title}</ToastTitle>}
                {description && (
                  <ToastDescription>{description}</ToastDescription>
                )}
              </div>
            </div>
            {action}
            <ToastClose />
          </Toast>
        )
      })}
      <ToastViewport />
    </ToastProvider>
  )
}
